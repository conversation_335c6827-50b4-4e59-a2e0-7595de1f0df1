package com.pulse.drug_permission.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_permission.manager.dto.DrugSpecificationDetailUsageBaseDto;
import com.pulse.drug_permission.persist.dos.DrugSpecificationDetailUsage;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "fa76dc63-0047-4a47-b8fc-12c4f1be7785|DTO|BASE_CONVERTER")
public class DrugSpecificationDetailUsageBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public DrugSpecificationDetailUsageBaseDto
            convertFromDrugSpecificationDetailUsageToDrugSpecificationDetailUsageBaseDto(
                    DrugSpecificationDetailUsage drugSpecificationDetailUsage) {
        return convertFromDrugSpecificationDetailUsageToDrugSpecificationDetailUsageBaseDto(
                        List.of(drugSpecificationDetailUsage))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<DrugSpecificationDetailUsageBaseDto>
            convertFromDrugSpecificationDetailUsageToDrugSpecificationDetailUsageBaseDto(
                    List<DrugSpecificationDetailUsage> drugSpecificationDetailUsageList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugSpecificationDetailUsageList)) {
            return new ArrayList<>();
        }
        List<DrugSpecificationDetailUsageBaseDto> drugSpecificationDetailUsageBaseDtoList =
                new ArrayList<>();
        for (DrugSpecificationDetailUsage drugSpecificationDetailUsage :
                drugSpecificationDetailUsageList) {
            if (drugSpecificationDetailUsage == null) {
                continue;
            }
            DrugSpecificationDetailUsageBaseDto drugSpecificationDetailUsageBaseDto =
                    new DrugSpecificationDetailUsageBaseDto();
            drugSpecificationDetailUsageBaseDto.setId(drugSpecificationDetailUsage.getId());
            drugSpecificationDetailUsageBaseDto.setDrugSpecificationDetailId(
                    drugSpecificationDetailUsage.getDrugSpecificationDetailId());
            drugSpecificationDetailUsageBaseDto.setUsePriority(
                    drugSpecificationDetailUsage.getUsePriority());
            drugSpecificationDetailUsageBaseDto.setDosage(drugSpecificationDetailUsage.getDosage());
            drugSpecificationDetailUsageBaseDto.setDosageUnit(
                    drugSpecificationDetailUsage.getDosageUnit());
            drugSpecificationDetailUsageBaseDto.setUsageDescription(
                    drugSpecificationDetailUsage.getUsageDescription());
            drugSpecificationDetailUsageBaseDto.setAdministration(
                    drugSpecificationDetailUsage.getAdministration());
            drugSpecificationDetailUsageBaseDto.setFrequency(
                    drugSpecificationDetailUsage.getFrequency());
            drugSpecificationDetailUsageBaseDto.setOutpFlag(
                    drugSpecificationDetailUsage.getOutpFlag());
            drugSpecificationDetailUsageBaseDto.setErpFlag(
                    drugSpecificationDetailUsage.getErpFlag());
            drugSpecificationDetailUsageBaseDto.setInpFlag(
                    drugSpecificationDetailUsage.getInpFlag());
            drugSpecificationDetailUsageBaseDto.setRemark(drugSpecificationDetailUsage.getRemark());
            drugSpecificationDetailUsageBaseDto.setUpdatedBy(
                    drugSpecificationDetailUsage.getUpdatedBy());
            drugSpecificationDetailUsageBaseDto.setInstitutionId(
                    drugSpecificationDetailUsage.getInstitutionId());
            drugSpecificationDetailUsageBaseDto.setCreatedBy(
                    drugSpecificationDetailUsage.getCreatedBy());
            drugSpecificationDetailUsageBaseDto.setUnit(drugSpecificationDetailUsage.getUnit());
            drugSpecificationDetailUsageBaseDto.setOutpAmount(
                    drugSpecificationDetailUsage.getOutpAmount());
            drugSpecificationDetailUsageBaseDto.setOutpDay(
                    drugSpecificationDetailUsage.getOutpDay());
            drugSpecificationDetailUsageBaseDto.setErpAmount(
                    drugSpecificationDetailUsage.getErpAmount());
            drugSpecificationDetailUsageBaseDto.setErpDay(drugSpecificationDetailUsage.getErpDay());
            drugSpecificationDetailUsageBaseDto.setDischargeMedicationDay(
                    drugSpecificationDetailUsage.getDischargeMedicationDay());
            drugSpecificationDetailUsageBaseDto.setLockVersion(
                    drugSpecificationDetailUsage.getLockVersion());
            drugSpecificationDetailUsageBaseDto.setCreatedAt(
                    drugSpecificationDetailUsage.getCreatedAt());
            drugSpecificationDetailUsageBaseDto.setUpdatedAt(
                    drugSpecificationDetailUsage.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            drugSpecificationDetailUsageBaseDtoList.add(drugSpecificationDetailUsageBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return drugSpecificationDetailUsageBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
