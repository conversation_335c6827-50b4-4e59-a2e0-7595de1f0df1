package com.pulse.file.service;

import com.pulse.file.manager.bo.*;
import com.pulse.file.manager.dto.FileMetadataBaseDto;
import com.pulse.file.persist.dos.FileMetadata;
import com.pulse.file.service.base.BaseFileMetadataBOService;
import com.pulse.file.service.bto.CreateFileMetadataBto;
import com.pulse.file.service.bto.DeleteFileMetadataBto;
import com.pulse.pulse.common.context.UserContext;
import com.vs.bo.AddedBto;
import com.vs.bo.DeletedBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import com.pulse.file.manager.bo.FileMetadataBO;

@Service
@Slf4j
@Validated
@AutoGenerated(locked = false, uuid = "85fa7111-b5c7-481a-aaf5-becbe6b7278b|BO|SERVICE")
public class FileMetadataBOService extends BaseFileMetadataBOService {
    @AutoGenerated(locked = true)
    @Resource
    private FileMetadataBaseDtoService fileMetadataBaseDtoService;

    /** 创建文件元数据 */
    @PublicInterface(id = "a067499b-fd68-47bb-ad75-b04c3a21c1b8", module = "file")
    @Transactional
    @AutoGenerated(locked = false, uuid = "8bcbdf11-405f-4268-9191-2297de05d029")
    public String createFileMetadata(@Valid @NotNull CreateFileMetadataBto createFileMetadataBto) {
        // 补全参数默认值
        fillDefaultValues(createFileMetadataBto);

        /** This block is generated by vs, do not modify, start anchor 1 */
        CreateFileMetadataBoResult boResult = super.createFileMetadataBase(createFileMetadataBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 CreateFileMetadataBto */
        {
            CreateFileMetadataBto bto =
                    boResult
                            .<CreateFileMetadataBto>getBtoOfType(CreateFileMetadataBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            AddedBto<CreateFileMetadataBto, FileMetadataBO> addedBto = boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (created) { // getAddedResult
                // 合并后的待保存值
                FileMetadataBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 批量创建文件元信息：绑定文件信息和业务信息。在一个事务中全部成功或全部失败 */
    @PublicInterface(
            id = "e882814f-ba4d-4e43-b058-37084cd3f497",
            module = "file",
            moduleId = "71949d63-fc3b-412a-8a2e-4e0f7b10a405",
            version = "1748410668220",
            pubRpc = false)
    @AutoGenerated(locked = false, uuid = "e882814f-ba4d-4e43-b058-37084cd3f497")
    @Transactional
    public List<String> batchCreateFileMetadata(
            @Valid @NotNull List<CreateFileMetadataBto> createFileMetadataList) {
        // 校验输入参数
        validateBatchCreateInput(createFileMetadataList);

        List<String> createdFileIds = new ArrayList<>();

        try {
            // 批量处理文件元数据创建 - 全部成功或全部失败
            createdFileIds = processBatchFileMetadataCreateAllOrNothing(createFileMetadataList);

            // 记录批量创建成功结果
            log.info("批量创建文件元数据全部成功：总数 {}", createFileMetadataList.size());

            return createdFileIds;

        } catch (Exception e) {
            // 记录批量创建失败，事务会自动回滚
            log.error("批量创建文件元数据失败，事务已回滚：总数 {}, 错误: {}",
                    createFileMetadataList.size(), e.getMessage(), e);

            // 重新抛出异常，确保事务回滚
            throw new com.vs.ox.common.exception.IgnoredException(
                    com.vs.ox.common.exception.ErrorCode.BUSINESS_ERROR,
                    "批量创建文件元数据失败：" + e.getMessage());
        }
    }

    /** 删除文件元信息 */
    @PublicInterface(id = "4aab62f6-a067-4313-a14d-03a01ee0617f", module = "file")
    @Transactional
    @AutoGenerated(locked = false, uuid = "ff4f0d1c-6cda-405a-ab14-b5dd90407c80")
    public String deleteFileMetadata(@Valid @NotNull DeleteFileMetadataBto deleteFileMetadataBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        FileMetadataBaseDto fileMetadataBaseDto =
                fileMetadataBaseDtoService.getById(deleteFileMetadataBto.getId());
        DeleteFileMetadataBoResult boResult = super.deleteFileMetadataBase(deleteFileMetadataBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 DeleteFileMetadataBto */
        {
            DeleteFileMetadataBto bto =
                    boResult
                            .<DeleteFileMetadataBto>getBtoOfType(DeleteFileMetadataBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            DeletedBto<DeleteFileMetadataBto, FileMetadata> deletedBto =
                    boResult.getDeletedResult(bto);
            boolean deleted = (deletedBto != null);
            if (deleted) { // getDeletedResult
                Object entity = deletedBto.getEntity();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /**
     * 校验批量创建输入参数
     *
     * @param createFileMetadataList 创建文件元数据的参数列表
     */
    private void validateBatchCreateInput(List<CreateFileMetadataBto> createFileMetadataList) {
        if (createFileMetadataList == null) {
            throw new IgnoredException(
                    ErrorCode.WRONG_PARAMETER, "文件元数据列表不能为空");
        }

        if (createFileMetadataList.isEmpty()) {
            throw new IgnoredException(
                    ErrorCode.WRONG_PARAMETER, "文件元数据列表不能为空");
        }

        // 检查列表大小限制，防止批量操作过大影响性能
        if (createFileMetadataList.size() > 100) {
            throw new IgnoredException(
                    ErrorCode.WRONG_PARAMETER, "批量创建文件元数据数量不能超过100个");
        }
    }

    /**
     * 处理批量文件元数据创建的核心逻辑 - 全部成功或全部失败
     *
     * @param createFileMetadataList 文件元数据创建参数列表
     * @return 创建成功的文件ID列表
     * @throws Exception 任何一个创建失败都会抛出异常，触发事务回滚
     */
    private List<String> processBatchFileMetadataCreateAllOrNothing(
            List<CreateFileMetadataBto> createFileMetadataList) throws Exception {
        List<String> createdFileIds = new ArrayList<>();

        // 预校验所有参数，确保在开始创建前所有参数都是有效的
        preValidateAllFileMetadata(createFileMetadataList);

        // 逐个创建文件元数据，任何一个失败都会抛出异常
        for (int i = 0; i < createFileMetadataList.size(); i++) {
            CreateFileMetadataBto createFileMetadataBto = createFileMetadataList.get(i);

            try {
                // 创建单个文件元数据（不使用事务，因为外层已有事务）
                String fileId = createSingleFileMetadata(createFileMetadataBto);
                createdFileIds.add(fileId);

                log.debug("文件元数据创建成功. FileId: {}, 序号: {}/{}",
                        fileId, i + 1, createFileMetadataList.size());

            } catch (Exception e) {
                // 任何一个创建失败都抛出异常，触发整个事务回滚
                String errorMsg = String.format("第%d个文件元数据创建失败，文件名: %s",
                        i + 1,
                        createFileMetadataBto != null ? createFileMetadataBto.getName() : "未知");

                log.error("批量创建文件元数据中断：{}, 错误: {}", errorMsg, e.getMessage(), e);

                // 抛出包装后的异常，包含更详细的错误信息
                throw new Exception(errorMsg + "，原因：" + e.getMessage(), e);
            }
        }

        return createdFileIds;
    }

    /**
     * 校验单个文件元数据创建参数
     *
     * @param createFileMetadataBto 文件元数据创建参数
     * @param index 在列表中的索引位置（从1开始）
     * @return 是否有效
     */
    private boolean isValidFileMetadataForCreate(CreateFileMetadataBto createFileMetadataBto, int index) {
        if (createFileMetadataBto == null) {
            log.warn("批量创建文件元数据：第{}个参数为空，跳过处理", index);
            return false;
        }

        // 可以添加更多业务校验逻辑
        return true;
    }

    /**
     * 创建单个文件元数据（内部方法，不使用事务）
     *
     * @param createFileMetadataBto 创建文件元数据的参数对象
     * @return 创建的文件元数据ID
     */
    private String createSingleFileMetadata(CreateFileMetadataBto createFileMetadataBto) {
        // 补全参数默认值
        fillDefaultValues(createFileMetadataBto);

        /** This block is generated by vs, do not modify, start anchor 1 */
        CreateFileMetadataBoResult boResult = super.createFileMetadataBase(createFileMetadataBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        /** 处理 CreateFileMetadataBto */
        {
            CreateFileMetadataBto bto =
                    boResult
                            .<CreateFileMetadataBto>getBtoOfType(CreateFileMetadataBto.class)
                            .stream()
                            .findAny()
                            .orElse(null);
            AddedBto<CreateFileMetadataBto, FileMetadataBO> addedBto = boResult.getAddedResult(bto);
            boolean created = (addedBto != null);
            if (created) { // getAddedResult
                // 合并后的待保存值
                FileMetadataBO bo = addedBto.getBo();
                // 其他自定义操作...
            }
        }
        /** This block is generated by vs, do not modify, start anchor 2 */
        if (boResult.getRootBo() != null) {
            boResult.getRootBo().persist();
        }
        return boResult.getRootBo() != null ? boResult.getRootBo().getId() : null;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /**
     * 处理单个文件元数据创建失败的情况
     *
     * @param createFileMetadataBto 创建失败的文件元数据参数
     * @param failedItems 失败项目列表
     * @param e 异常信息
     * @param index 在列表中的索引位置（从1开始）
     */
    private void handleSingleFileMetadataCreateFailure(
            CreateFileMetadataBto createFileMetadataBto,
            List<String> failedItems,
            Exception e,
            int index) {
        String failureInfo = String.format("第%d个文件元数据创建失败", index);
        failedItems.add(failureInfo);

        log.error("批量创建文件元数据：{}，文件名: {}, 错误: {}",
                failureInfo,
                createFileMetadataBto != null ? createFileMetadataBto.getName() : "未知",
                e.getMessage(), e);
    }

    /**
     * 记录批量创建结果
     *
     * @param totalCount 总数量
     * @param successCount 成功数量
     * @param failedItems 失败项目列表
     */
    private void logBatchCreateResult(int totalCount, int successCount, List<String> failedItems) {
        if (failedItems.isEmpty()) {
            log.info("批量创建文件元数据完成：总数 {}, 全部成功", totalCount);
        } else {
            log.warn("批量创建文件元数据完成：总数 {}, 成功 {}, 失败 {}, 失败详情: {}",
                    totalCount, successCount, failedItems.size(), failedItems);
        }
    }

    /**
     * 补全创建文件元数据参数的默认值
     *
     * @param createFileMetadataBto 创建文件元数据的参数对象
     */
    private void fillDefaultValues(CreateFileMetadataBto createFileMetadataBto) {
        // 补全加密标识，默认为false（不加密）
        if (createFileMetadataBto.getEncrypted() == null) {
            createFileMetadataBto.setEncrypted(false);
        }

        // 补全加密密钥，当不加密时设置为null
        if (createFileMetadataBto.getEncryptionKey() == null) {
            if (Boolean.FALSE.equals(createFileMetadataBto.getEncrypted())) {
                createFileMetadataBto.setEncryptionKey(null);
            }
        }

        // 补全上传者ID，优先使用当前登录用户ID，如果为空则设置为"system"
        if (createFileMetadataBto.getUploadBy() == null) {
            String userId = UserContext.getUserId();
            if (userId == null || userId.trim().isEmpty()) {
                userId = "system"; // 默认设置为system，表示是系统操作
            }
            createFileMetadataBto.setUploadBy(userId);
        }
    }
}
