package com.pulse.file.entrance.web.controller;

import com.pulse.file.common.enums.FileBusinessTypeEnum;
import com.pulse.file.entrance.web.converter.FileMetadataBaseVoConverter;
import com.pulse.file.entrance.web.vo.FileMetadataBaseVo;
import com.pulse.file.manager.dto.FileMetadataBaseDto;
import com.pulse.file.persist.eo.FileBusinessEo;
import com.pulse.file.service.FileMetadataBOService;
import com.pulse.file.service.FileService;
import com.pulse.file.service.bto.CreateFileMetadataBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/** 文件控制器 负责处理文件上传、下载、删除和获取文件URL等操作的HTTP请求 */
@Slf4j
@Controller
@Validated
@AutoGenerated(locked = false, uuid = "e37f47e3-5836-3bed-9001-a26330d60ffb")
public class FileController {
    @Resource
    private FileService fileService;

    @AutoGenerated(locked = true)
    @Resource
    private FileMetadataBOService fileMetadataBOService;

    @Resource
    private FileMetadataBaseVoConverter fileMetadataBaseVoConverter;

    /** 删除文件及其元数据（使用默认存储类型） */
    @PublicInterface(id = "35ace999-0623-44c1-8972-300070ed6b65", version = "1748322600124")
    @AutoGenerated(locked = false, uuid = "35ace999-0623-44c1-8972-300070ed6b65")
    @RequestMapping(
            value = {"/api/file/delete-file"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String deleteFile(@Valid FileBusinessEo fileBusiness) {
        return fileService.deleteFile(fileBusiness);
    }

    /** 上传文件并关联业务信息 上传文件并关联业务信息，返回新创建的文件ID。第一个参数需传入MultipartFile file参数 */
    @PublicInterface(id = "3cd66767-43de-49a5-8b3c-8d327e48dd9a", version = "1748409362682")
    @AutoGenerated(locked = false, uuid = "3cd66767-43de-49a5-8b3c-8d327e48dd9a")
    @RequestMapping(
            value = {"/api/file/upload-file-with-business"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String uploadFileWithBusiness(
            MultipartFile file,
            @NotNull String businessId,
            @NotNull FileBusinessTypeEnum businessType) {
        FileBusinessEo fileBusiness = new FileBusinessEo();
        fileBusiness.setBusinessId(businessId);
        fileBusiness.setBusinessType(businessType);
        return fileService.uploadFileWithBusiness(file, fileBusiness);
    }

    /** 根据单个业务信息获取文件URL列表 根据单个业务信息获取文件URL列表：可根据业务ID+业务类型、文件ID（可选）获取文件URL列表 */
    @PublicInterface(id = "573c9d1b-b96e-4df6-9361-2032b4554e78", version = "1748322704542")
    @AutoGenerated(locked = false, uuid = "573c9d1b-b96e-4df6-9361-2032b4554e78")
    @RequestMapping(
            value = {"/api/file/list-file-url-by-business"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<String> listFileUrlByBusiness(@Valid @NotNull FileBusinessEo fileBusiness) {
        return fileService.listFileUrlByBusiness(fileBusiness);
    }

    /** 上传文件（不绑定业务信息） 上传文件（不绑定业务信息），返回完整的文件元数据信息（除业务信息外的所有属性）。第一个参数需传入MultipartFile file参数 */
    @PublicInterface(id = "59d99294-4244-40e6-ad23-99f8f5eceae7", version = "1748323009007")
    @AutoGenerated(locked = false, uuid = "59d99294-4244-40e6-ad23-99f8f5eceae7")
    @RequestMapping(
            value = {"/api/file/upload-file-without-business"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public FileMetadataBaseVo uploadFileWithoutBusiness(MultipartFile file) {
        FileMetadataBaseDto fileMetadataBaseDto = fileService.uploadFileWithoutBusiness(file);
        return fileMetadataBaseVoConverter.convertToFileMetadataBaseVo(fileMetadataBaseDto);
    }

    /** 批量删除文件及其元数据（使用默认存储类型） */
    @PublicInterface(id = "*************-4818-a83d-d8df74766a82", version = "1748322579487")
    @AutoGenerated(locked = false, uuid = "*************-4818-a83d-d8df74766a82")
    @RequestMapping(
            value = {"/api/file/batch-delete-file"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<String> batchDeleteFile(@Valid @NotNull List<FileBusinessEo> fileBusinessList) {
        return fileService.batchDeleteFile(fileBusinessList);
    }

    /** 批量创建文件元信息 批量创建文件元信息：绑定文件信息和业务信息。返回创建成功的文件元信息ID列表 */
    @PublicInterface(id = "75eb7fd5-1dc9-4e0f-8067-afb043637549", version = "1748410731124")
    @AutoGenerated(locked = false, uuid = "75eb7fd5-1dc9-4e0f-8067-afb043637549")
    @RequestMapping(
            value = {"/api/file/batch-create-file-metadata"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<String> batchCreateFileMetadata(
            @Valid List<CreateFileMetadataBto> createFileMetadataList) {
        return fileMetadataBOService.batchCreateFileMetadata(createFileMetadataList);
    }

    /** 创建文件元数据 创建文件元数据：绑定文件信息和业务信息 */
    @PublicInterface(id = "9eae3f59-bed3-4b1c-8d2b-cd0ca851df24", version = "1748236887284")
    @AutoGenerated(locked = false, uuid = "9eae3f59-bed3-4b1c-8d2b-cd0ca851df24")
    @RequestMapping(
            value = {"/api/file/create-file-metadata"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String createFileMetadata(@Valid CreateFileMetadataBto createFileMetadataBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = fileMetadataBOService.createFileMetadata(createFileMetadataBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 下载文件内容（使用默认存储类型） 下载文件内容（使用默认存储类型），文件ID必传，仅支持单个文件下载 */
    @PublicInterface(id = "f6123411-**************-004118c44139", version = "1748322900616")
    @AutoGenerated(locked = false, uuid = "f6123411-**************-004118c44139")
    @RequestMapping(
            value = {"/api/file/download-file"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public byte[] downloadFile(@Valid @NotNull FileBusinessEo fileBusiness) {
        if (fileBusiness.getFileId() == null || fileBusiness.getFileId().isEmpty()) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "文件ID不能为空");
        }

        return fileService.downloadFile(fileBusiness);
    }
}
