package com.pulse.pivas_preparation.manager.converter.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.pivas_preparation.manager.dto.DrugPivasTrackingBaseDto;
import com.pulse.pivas_preparation.persist.dos.DrugPivasTracking;
import com.vs.code.AutoGenerated;

import java.util.ArrayList;
import java.util.List;

@AutoGenerated(locked = false, uuid = "2b08ff8a-8f76-4664-bfa3-e25dfdccce86|DTO|BASE_CONVERTER")
public class DrugPivasTrackingBaseDtoBaseConverter {

    @AutoGenerated(locked = true)
    public DrugPivasTrackingBaseDto convertFromDrugPivasTrackingToDrugPivasTrackingBaseDto(
            DrugPivasTracking drugPivasTracking) {
        return convertFromDrugPivasTrackingToDrugPivasTrackingBaseDto(List.of(drugPivasTracking))
                .stream()
                .findAny()
                .orElse(null);
    }

    @AutoGenerated(locked = true)
    public List<DrugPivasTrackingBaseDto> convertFromDrugPivasTrackingToDrugPivasTrackingBaseDto(
            List<DrugPivasTracking> drugPivasTrackingList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugPivasTrackingList)) {
            return new ArrayList<>();
        }
        List<DrugPivasTrackingBaseDto> drugPivasTrackingBaseDtoList = new ArrayList<>();
        for (DrugPivasTracking drugPivasTracking : drugPivasTrackingList) {
            if (drugPivasTracking == null) {
                continue;
            }
            DrugPivasTrackingBaseDto drugPivasTrackingBaseDto = new DrugPivasTrackingBaseDto();
            drugPivasTrackingBaseDto.setId(drugPivasTracking.getId());
            drugPivasTrackingBaseDto.setOrderPerformPlanId(
                    drugPivasTracking.getOrderPerformPlanId());
            drugPivasTrackingBaseDto.setOrderId(drugPivasTracking.getOrderId());
            drugPivasTrackingBaseDto.setDrugOriginSpecificationId(
                    drugPivasTracking.getDrugOriginSpecificationId());
            drugPivasTrackingBaseDto.setInfusionNumber(drugPivasTracking.getInfusionNumber());
            drugPivasTrackingBaseDto.setAmount(drugPivasTracking.getAmount());
            drugPivasTrackingBaseDto.setStatus(drugPivasTracking.getStatus());
            drugPivasTrackingBaseDto.setBatchSettingId(drugPivasTracking.getBatchSettingId());
            drugPivasTrackingBaseDto.setDispenseDetailId(drugPivasTracking.getDispenseDetailId());
            drugPivasTrackingBaseDto.setDistributeId(drugPivasTracking.getDistributeId());
            drugPivasTrackingBaseDto.setSummaryPrintCount(drugPivasTracking.getSummaryPrintCount());
            drugPivasTrackingBaseDto.setLabelPrintCount(drugPivasTracking.getLabelPrintCount());
            drugPivasTrackingBaseDto.setStorageCode(drugPivasTracking.getStorageCode());
            drugPivasTrackingBaseDto.setCreatedAt(drugPivasTracking.getCreatedAt());
            drugPivasTrackingBaseDto.setUpdatedAt(drugPivasTracking.getUpdatedAt());
            /** This block is generated by vs, do not modify, end anchor 1 */
            // 自定义处理逻辑，循环内建议放置静态代码
            /** This block is generated by vs, do not modify, start anchor 2 */
            drugPivasTrackingBaseDtoList.add(drugPivasTrackingBaseDto);
        }
        /** This block is generated by vs, do not modify, end anchor 2 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 3 */
        return drugPivasTrackingBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 3 */
    }
}
