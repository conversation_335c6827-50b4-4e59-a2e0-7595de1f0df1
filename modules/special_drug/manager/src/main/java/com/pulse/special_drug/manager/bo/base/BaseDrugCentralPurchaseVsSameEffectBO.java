package com.pulse.special_drug.manager.bo.base;

import com.pulse.special_drug.manager.bo.DrugCentralPurchaseVsSameEffectBO;
import com.pulse.special_drug.manager.bo.DrugSameEffectWhitelistBO;
import com.pulse.special_drug.persist.dos.DrugCentralPurchaseVsSameEffect;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;
import com.vs.code.DoNotModify;
import com.vs.persist.transactional.TransactionalSessionFactory;

import org.hibernate.Session;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.MappedSuperclass;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.Version;

@DoNotModify
@Table(name = "drug_central_purchase_vs_same_effect")
@MappedSuperclass
@AutoGenerated(locked = true, uuid = "a23fada3-53a5-320e-856f-3340953abea2")
public abstract class BaseDrugCentralPurchaseVsSameEffectBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 集采药品产地编码 原药品价格ID(集采价格ID) */
    @Column(name = "central_purchase_origin_code")
    @AutoGenerated(locked = true, uuid = "cc265ad6-89df-3369-8f22-f13e5d5d6b57")
    private String centralPurchaseOriginCode;

    /** 创建时间 */
    @Column(name = "created_at")
    @AutoGenerated(locked = true, uuid = "29e11abf-e63c-3f84-84b0-a4847217fe39")
    private Date createdAt;

    /** 创建人id */
    @Column(name = "created_by")
    @AutoGenerated(locked = true, uuid = "b77af0ad-195d-3aad-abb3-745e2adee9f9")
    private String createdBy;

    @JoinColumn(name = "central_purchase_vs_same_effect_id", updatable = false)
    @AutoGenerated(locked = true)
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<DrugSameEffectWhitelistBO> drugSameEffectWhitelistBOSet = new HashSet<>();

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "d6ac3ab2-c20d-3d36-90e7-e6584f96f98a")
    @Id
    private String id;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    /** 乐观锁字段 乐观锁字段 */
    @Column(name = "lock_version")
    @AutoGenerated(locked = true, uuid = "2c376d12-67c0-4472-a44e-af0c0a38154a")
    @Version
    private Long lockVersion;

    /** 竞品药品产地编码 竞品药品ID */
    @Column(name = "same_effect_origin_code")
    @AutoGenerated(locked = true, uuid = "22aa94dc-6f0d-3e60-9ab7-475e29a48f07")
    private String sameEffectOriginCode;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "39365da9-7813-382c-b049-0cc6d1b9efde")
    private Date updatedAt;

    /** 修改人id */
    @Column(name = "updated_by")
    @AutoGenerated(locked = true, uuid = "83ff0998-6626-3639-a419-8cecc0323e02")
    private String updatedBy;

    @AutoGenerated(locked = true)
    public DrugCentralPurchaseVsSameEffect convertToDrugCentralPurchaseVsSameEffect() {
        DrugCentralPurchaseVsSameEffect entity = new DrugCentralPurchaseVsSameEffect();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "centralPurchaseOriginCode",
                "sameEffectOriginCode",
                "createdBy",
                "updatedBy",
                "lockVersion",
                "createdAt",
                "updatedAt");
        return entity;
    }

    @AutoGenerated(locked = true)
    public void delete() {
        Session session = TransactionalSessionFactory.getSession();
        session.delete(this);
    }

    @AutoGenerated(locked = true)
    public static DrugCentralPurchaseVsSameEffectBO getById(String id) {
        Session session = TransactionalSessionFactory.getSession();
        DrugCentralPurchaseVsSameEffectBO drugCentralPurchaseVsSameEffect =
                (DrugCentralPurchaseVsSameEffectBO)
                        session.createQuery(
                                        "from DrugCentralPurchaseVsSameEffectBO where "
                                                + "id =: id ")
                                .setParameter("id", id)
                                .uniqueResult();
        return drugCentralPurchaseVsSameEffect;
    }

    @AutoGenerated(locked = true)
    public String getCentralPurchaseOriginCode() {
        return this.centralPurchaseOriginCode;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public String getCreatedBy() {
        return this.createdBy;
    }

    @AutoGenerated(locked = true)
    public Set<DrugSameEffectWhitelistBO> getDrugSameEffectWhitelistBOSet() {
        return this.drugSameEffectWhitelistBOSet;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    private Long getLockVersion() {
        return this.lockVersion;
    }

    @AutoGenerated(locked = true)
    public String getSameEffectOriginCode() {
        return this.sameEffectOriginCode;
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public String getUpdatedBy() {
        return this.updatedBy;
    }

    @AutoGenerated(locked = true)
    public void persist() {
        this.validateAggregate();
        Session session = TransactionalSessionFactory.getSession();
        session.saveOrUpdate(this);
        session.flush();
    }

    @AutoGenerated(locked = true)
    public DrugCentralPurchaseVsSameEffectBO setCentralPurchaseOriginCode(
            String centralPurchaseOriginCode) {
        this.centralPurchaseOriginCode = centralPurchaseOriginCode;
        return (DrugCentralPurchaseVsSameEffectBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugCentralPurchaseVsSameEffectBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (DrugCentralPurchaseVsSameEffectBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugCentralPurchaseVsSameEffectBO setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
        return (DrugCentralPurchaseVsSameEffectBO) this;
    }

    @AutoGenerated(locked = true)
    private void setDrugSameEffectWhitelistBOSet(
            Set<DrugSameEffectWhitelistBO> drugSameEffectWhitelistBOSet) {
        this.drugSameEffectWhitelistBOSet = drugSameEffectWhitelistBOSet;
    }

    @AutoGenerated(locked = true)
    public DrugCentralPurchaseVsSameEffectBO setId(String id) {
        this.id = id;
        return (DrugCentralPurchaseVsSameEffectBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugCentralPurchaseVsSameEffectBO setLockVersion(Long lockVersion) {
        this.lockVersion = lockVersion;
        return (DrugCentralPurchaseVsSameEffectBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugCentralPurchaseVsSameEffectBO setSameEffectOriginCode(String sameEffectOriginCode) {
        this.sameEffectOriginCode = sameEffectOriginCode;
        return (DrugCentralPurchaseVsSameEffectBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugCentralPurchaseVsSameEffectBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (DrugCentralPurchaseVsSameEffectBO) this;
    }

    @AutoGenerated(locked = true)
    public DrugCentralPurchaseVsSameEffectBO setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
        return (DrugCentralPurchaseVsSameEffectBO) this;
    }

    /** 当聚合有任何变更（删除聚合根除外）的时候触发回调 */
    @AutoGenerated(locked = true)
    protected void validateAggregate() {}
}
