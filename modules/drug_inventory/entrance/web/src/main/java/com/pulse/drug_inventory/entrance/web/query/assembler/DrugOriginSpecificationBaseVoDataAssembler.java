package com.pulse.drug_inventory.entrance.web.query.assembler;

import com.pulse.drug_inventory.entrance.web.vo.DrugOriginSpecificationBaseVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** DrugOriginSpecificationBaseVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "3213887e-1cff-3fc3-ba50-879109bc0d3d")
public class DrugOriginSpecificationBaseVoDataAssembler {

    /** 批量自定义组装DrugOriginSpecificationBaseVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "c2265ca9-e728-3054-93b6-4407326c9fb5")
    public void assembleDataCustomized(List<DrugOriginSpecificationBaseVo> dataList) {
        // 自定义数据组装

    }

    /** 组装DrugOriginSpecificationBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "efae6ec1-3008-33c2-8689-28ef3f9e191a")
    public void assembleData(Map<String, DrugOriginSpecificationBaseVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }
}
