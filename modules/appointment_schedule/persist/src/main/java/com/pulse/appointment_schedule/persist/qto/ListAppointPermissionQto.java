package com.pulse.appointment_schedule.persist.qto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AutoGenerated(locked = true, uuid = "0c907e2a-de18-4dd5-b46f-eeb75528450e|QTO|DEFINITION")
public class ListAppointPermissionQto {
    /** 职工id appoint_permission.doctor_id */
    @AutoGenerated(locked = true, uuid = "caba4f98-882d-44eb-95f5-6474afa85a2d")
    private String departmentIdIs;

    /** 启用标志 appoint_permission.enable_flag */
    @AutoGenerated(locked = true, uuid = "7f812ef7-4eb8-44b2-88fc-845dad3cfe01")
    private Boolean enableFlagIs;

    @AutoGenerated(locked = true, uuid = "d47c0692-f14f-4282-b7a6-a82ed499b084")
    private Integer from;

    @AutoGenerated(locked = true, uuid = "3dea2f2d-bab9-4edc-b6b7-65fa1fabbbd4")
    private Integer size;
}
