package com.pulse.appointment_schedule.entrance.web.query.assembler;

import com.pulse.appointment_schedule.entrance.web.vo.ClinicRegisterItemPriceVo;
import com.pulse.appointment_schedule.entrance.web.vo.ClinicRegisterPriceItemVo;
import com.pulse.appointment_schedule.manager.dto.ClinicRegisterPriceBaseDto;
import com.pulse.appointment_schedule.manager.dto.ClinicRegisterTypeBaseDto;
import com.pulse.appointment_schedule.service.ClinicRegisterTypeBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** ClinicRegisterItemPriceVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "b6499eec-ddcb-37f3-8e23-3626ace2ba72")
public class ClinicRegisterItemPriceVoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private ClinicRegisterTypeBaseDtoService clinicRegisterTypeBaseDtoService;

    /** 组装ClinicRegisterItemPriceVo数据 */
    @AutoGenerated(locked = true, uuid = "1b840589-b74d-3a1a-ad2f-4e4bbad9be85")
    public void assembleData(
            Map<String, ClinicRegisterItemPriceVo> voMap,
            ClinicRegisterItemPriceVoDataAssembler.ClinicRegisterItemPriceVoDataHolder dataHolder) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        List<ClinicRegisterTypeBaseDto> baseDtoList = dataHolder.getRootBaseDtoList();

        Map<String, List<Pair<ClinicRegisterPriceBaseDto, ClinicRegisterPriceItemVo>>>
                clinicRegisterPriceItemList =
                        dataHolder.clinicRegisterPriceItemList.keySet().stream()
                                .collect(
                                        Collectors.groupingBy(
                                                dto -> dto.getClinicRegisterTypeId(),
                                                Collectors.mapping(
                                                        dto ->
                                                                Pair.of(
                                                                        dto,
                                                                        dataHolder
                                                                                .clinicRegisterPriceItemList
                                                                                .get(dto)),
                                                        Collectors.toCollection(ArrayList::new))));

        for (ClinicRegisterTypeBaseDto baseDto : baseDtoList) {
            ClinicRegisterItemPriceVo vo = voMap.get(baseDto.getId());
            vo.setClinicRegisterPriceItemList(
                    Optional.ofNullable(clinicRegisterPriceItemList.get(baseDto.getId()))
                            .map(
                                    tmp ->
                                            tmp.stream()
                                                    .map(pair -> pair.getRight())
                                                    .collect(Collectors.toList()))
                            .orElse(new ArrayList<>()));
        }

        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装ClinicRegisterItemPriceVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "f5a23e84-8eb8-3257-bae5-3ccc7da79222")
    public void assembleDataCustomized(List<ClinicRegisterItemPriceVo> dataList) {
        // 自定义数据组装

    }

    /** 持有vo需要的全部Dto数据 */
    @Getter
    @Setter
    public static class ClinicRegisterItemPriceVoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<ClinicRegisterTypeBaseDto> rootBaseDtoList;

        /** 持有字段clinicRegisterPriceItemList的Dto数据 */
        @AutoGenerated(locked = true)
        public Map<ClinicRegisterPriceBaseDto, ClinicRegisterPriceItemVo>
                clinicRegisterPriceItemList;
    }
}
