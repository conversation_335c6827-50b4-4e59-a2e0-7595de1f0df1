package com.pulse.appointment_schedule.entrance.web.vo;

import com.pulse.appointment_schedule.common.enums.SchedulingstatusEnum;
import com.pulse.appointment_schedule.common.enums.TimeDescriptionEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "a664fd2b-5bde-412e-8c33-ff034fbeca4d|VO|DEFINITION")
public class AppointmentScheduleRoomsVo {
    /** 加号 */
    @AutoGenerated(locked = true, uuid = "52cd1e88-c878-4e02-b10b-998d09fb7195")
    private Long addNumber;

    /** 应用ID */
    @AutoGenerated(locked = true, uuid = "6f642b28-3f01-41c6-8bde-0140d336d555")
    private String appId;

    /** 院区id */
    @AutoGenerated(locked = true, uuid = "9106c84b-d5e3-4e60-b01f-c0578bd36c8c")
    private String branchInstitutionId;

    /** 挂号类别id */
    @AutoGenerated(locked = true, uuid = "fac9faa3-6ed5-4b8a-b712-30f82cf5a676")
    private String clinicRegisterTypeId;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "73520aea-16a4-4f2d-ab3c-a395119cf802")
    private Date createdAt;

    /** 创建人 */
    @AutoGenerated(locked = true, uuid = "e7941b03-c604-4f56-85d0-e65a8da77c8b")
    private String createdBy;

    /** 星期 */
    @AutoGenerated(locked = true, uuid = "9eead55e-482b-4c60-a6e4-45547812ef79")
    private String dayOfWeek;

    /** 科室id */
    @AutoGenerated(locked = true, uuid = "615448d1-b5d9-49b8-ba76-65ea54e7bed8")
    private String departmentId;

    /** 医生id */
    @AutoGenerated(locked = true, uuid = "1d81ce03-bf7f-4c6b-aa8b-b4a332c4d174")
    private String doctorId;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "e0509ef0-74c1-47e3-8e7c-93f3f4201ff6")
    private String id;

    /** 限号 */
    @AutoGenerated(locked = true, uuid = "4e1f4ce4-6449-4400-8c56-dc9755703ea7")
    private Long limitNumber;

    /** 排班日期 */
    @AutoGenerated(locked = true, uuid = "e00c64cf-c5d5-4572-b19a-25ec40ebf09d")
    private Date registerTime;

    /** 备注 */
    @AutoGenerated(locked = true, uuid = "ccc7569c-6a58-4a2e-b557-f982139a3e9c")
    private String remark;

    /** 审核标志 */
    @AutoGenerated(locked = true, uuid = "d77451bb-a793-4cbe-bcff-ce70f360c3dd")
    private Boolean reviewFlag;

    /** 诊室id */
    @Valid
    @AutoGenerated(locked = true, uuid = "17d5606f-0290-46f4-9a84-f2c889fe48d7")
    private ConsultingRoomAllVo room;

    /** 排班计划ID */
    @AutoGenerated(locked = true, uuid = "910f0e1b-38b9-4624-b22e-fcd6b092f096")
    private String schedulePlanId;

    /** 排班业务类型 */
    @AutoGenerated(locked = true, uuid = "07116391-5fd6-44a4-877c-eb74e0c86eed")
    private String schedulingBusinessType;

    /** 排班模板ID */
    @AutoGenerated(locked = true, uuid = "833a94cf-a74d-45a1-a1b3-8f4b8f196e01")
    private String schedulingTemplateId;

    /** 状态 */
    @AutoGenerated(locked = true, uuid = "2c65e780-b674-40a8-9eb9-84314e6c6331")
    private SchedulingstatusEnum status;

    /** 午别 */
    @AutoGenerated(locked = true, uuid = "16881e0d-b14c-4ef7-b545-d5a0a951410a")
    private TimeDescriptionEnum timeDescription;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "da3a8479-8cbc-4986-a239-eb975d7a0204")
    private Date updatedAt;

    /** 修改人 */
    @AutoGenerated(locked = true, uuid = "4325efa0-4458-489f-b27b-2a86161d1ef8")
    private String updatedBy;
}
