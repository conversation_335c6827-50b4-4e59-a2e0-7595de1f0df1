package com.pulse.diagnosis.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.diagnosis.manager.DiagnosisRecordBaseDtoManager;
import com.pulse.diagnosis.manager.DiagnosisRecordDictionaryExtDtoManager;
import com.pulse.diagnosis.manager.converter.DiagnosisRecordBaseDtoConverter;
import com.pulse.diagnosis.manager.converter.DiagnosisRecordDictionaryExtDtoConverter;
import com.pulse.diagnosis.manager.dto.DiagnosisRecordBaseDto;
import com.pulse.diagnosis.manager.dto.DiagnosisRecordDictionaryExtDto;
import com.pulse.diagnosis.manager.facade.dictionary_business.DiagnosisDictionaryBaseDtoServiceInDiagnosisRpcAdapter;
import com.pulse.diagnosis.persist.dos.DiagnosisRecord;
import com.pulse.diagnosis.persist.mapper.DiagnosisRecordDao;
import com.pulse.dictionary_business.manager.dto.DiagnosisDictionaryBaseDto;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "99877868-307a-4643-b00c-94da06237045|DTO|BASE_MANAGER_IMPL")
public abstract class DiagnosisRecordDictionaryExtDtoManagerBaseImpl
        implements DiagnosisRecordDictionaryExtDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private DiagnosisDictionaryBaseDtoServiceInDiagnosisRpcAdapter
            diagnosisDictionaryBaseDtoServiceInDiagnosisRpcAdapter;

    @AutoGenerated(locked = true)
    @Autowired
    private DiagnosisRecordBaseDtoConverter diagnosisRecordBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private DiagnosisRecordBaseDtoManager diagnosisRecordBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private DiagnosisRecordDao diagnosisRecordDao;

    @AutoGenerated(locked = true)
    @Autowired
    private DiagnosisRecordDictionaryExtDtoConverter diagnosisRecordDictionaryExtDtoConverter;

    @AutoGenerated(locked = true, uuid = "0628fead-459e-3a39-a092-e5fbacd532cf")
    @Override
    public List<DiagnosisRecordDictionaryExtDto> getByDiagnosisId(String diagnosisId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DiagnosisRecordDictionaryExtDto> diagnosisRecordDictionaryExtDtoList =
                getByDiagnosisIds(Arrays.asList(diagnosisId));
        return diagnosisRecordDictionaryExtDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "3c0d19b9-5d2e-3196-b8a1-f4eadac076df")
    @Override
    public List<DiagnosisRecordDictionaryExtDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<DiagnosisRecord> diagnosisRecordList = diagnosisRecordDao.getByIds(id);
        if (CollectionUtil.isEmpty(diagnosisRecordList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, DiagnosisRecord> diagnosisRecordMap =
                diagnosisRecordList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        diagnosisRecordList =
                id.stream()
                        .map(i -> diagnosisRecordMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromDiagnosisRecordToDiagnosisRecordDictionaryExtDto(diagnosisRecordList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "672ae5da-c7d5-3952-8e22-8a6cf5ba2420")
    @Override
    public DiagnosisRecordDictionaryExtDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DiagnosisRecordDictionaryExtDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        DiagnosisRecordDictionaryExtDto diagnosisRecordDictionaryExtDto =
                CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return diagnosisRecordDictionaryExtDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "a9b6d1d4-10eb-36e8-b7ef-4ef82e344c64")
    @Override
    public List<DiagnosisRecordDictionaryExtDto> getByDiagnosisIds(List<String> diagnosisId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(diagnosisId)) {
            return Collections.emptyList();
        }

        List<DiagnosisRecord> diagnosisRecordList =
                diagnosisRecordDao.getByDiagnosisIds(diagnosisId);
        if (CollectionUtil.isEmpty(diagnosisRecordList)) {
            return Collections.emptyList();
        }

        return doConvertFromDiagnosisRecordToDiagnosisRecordDictionaryExtDto(diagnosisRecordList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "bfc5caf1-9429-374e-88a4-e5bdd29d7b41")
    public List<DiagnosisRecordDictionaryExtDto>
            doConvertFromDiagnosisRecordToDiagnosisRecordDictionaryExtDto(
                    List<DiagnosisRecord> diagnosisRecordList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(diagnosisRecordList)) {
            return Collections.emptyList();
        }

        Map<String, String> diagnosisIdMap =
                diagnosisRecordList.stream()
                        .filter(i -> i.getDiagnosisId() != null)
                        .collect(
                                Collectors.toMap(
                                        DiagnosisRecord::getId, DiagnosisRecord::getDiagnosisId));
        List<DiagnosisDictionaryBaseDto> diagnosisIdDiagnosisDictionaryBaseDtoList =
                diagnosisDictionaryBaseDtoServiceInDiagnosisRpcAdapter.getByIds(
                        new ArrayList<>(new HashSet<>(diagnosisIdMap.values())));
        Map<String, DiagnosisDictionaryBaseDto> diagnosisIdDiagnosisDictionaryBaseDtoMapRaw =
                diagnosisIdDiagnosisDictionaryBaseDtoList.stream()
                        .collect(Collectors.toMap(DiagnosisDictionaryBaseDto::getId, i -> i));
        Map<String, DiagnosisDictionaryBaseDto> diagnosisIdDiagnosisDictionaryBaseDtoMap =
                diagnosisIdMap.entrySet().stream()
                        .filter(
                                i ->
                                        diagnosisIdDiagnosisDictionaryBaseDtoMapRaw.get(
                                                        i.getValue())
                                                != null)
                        .collect(
                                Collectors.toMap(
                                        i -> i.getKey(),
                                        i ->
                                                diagnosisIdDiagnosisDictionaryBaseDtoMapRaw.get(
                                                        i.getValue())));

        List<DiagnosisRecordBaseDto> baseDtoList =
                diagnosisRecordBaseDtoConverter.convertFromDiagnosisRecordToDiagnosisRecordBaseDto(
                        diagnosisRecordList);
        Map<String, DiagnosisRecordDictionaryExtDto> dtoMap =
                diagnosisRecordDictionaryExtDtoConverter
                        .convertFromDiagnosisRecordBaseDtoToDiagnosisRecordDictionaryExtDto(
                                baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        DiagnosisRecordDictionaryExtDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<DiagnosisRecordDictionaryExtDto> diagnosisRecordDictionaryExtDtoList =
                new ArrayList<>();
        for (DiagnosisRecord i : diagnosisRecordList) {
            DiagnosisRecordDictionaryExtDto diagnosisRecordDictionaryExtDto = dtoMap.get(i.getId());
            if (diagnosisRecordDictionaryExtDto == null) {
                continue;
            }

            if (null != i.getDiagnosisId()) {
                diagnosisRecordDictionaryExtDto.setDiagnosis(
                        diagnosisIdDiagnosisDictionaryBaseDtoMap.getOrDefault(i.getId(), null));
            }
            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            diagnosisRecordDictionaryExtDtoList.add(diagnosisRecordDictionaryExtDto);
        }
        return diagnosisRecordDictionaryExtDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
