package com.pulse.certificate.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.certificate.manager.CertificateBaseDtoManager;
import com.pulse.certificate.manager.CertificateDetailDtoManager;
import com.pulse.certificate.manager.converter.CertificateBaseDtoConverter;
import com.pulse.certificate.manager.converter.CertificateDetailDtoConverter;
import com.pulse.certificate.manager.dto.CertificateBaseDto;
import com.pulse.certificate.manager.dto.CertificateDetailDto;
import com.pulse.certificate.manager.facade.patient_information.PatientBaseDtoServiceInCertificateRpcAdapter;
import com.pulse.certificate.manager.facade.visit.OutpVisitBaseDtoServiceInCertificateRpcAdapter;
import com.pulse.certificate.persist.dos.Certificate;
import com.pulse.certificate.persist.mapper.CertificateDao;
import com.pulse.patient_information.manager.dto.PatientBaseDto;
import com.pulse.visit.manager.dto.OutpVisitBaseDto;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "be3da4ea-55d6-47b4-a419-187c524bfcf6|DTO|BASE_MANAGER_IMPL")
public abstract class CertificateDetailDtoManagerBaseImpl implements CertificateDetailDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private CertificateBaseDtoConverter certificateBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private CertificateBaseDtoManager certificateBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private CertificateDao certificateDao;

    @AutoGenerated(locked = true)
    @Autowired
    private CertificateDetailDtoConverter certificateDetailDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private OutpVisitBaseDtoServiceInCertificateRpcAdapter
            outpVisitBaseDtoServiceInCertificateRpcAdapter;

    @AutoGenerated(locked = true)
    @Autowired
    private PatientBaseDtoServiceInCertificateRpcAdapter
            patientBaseDtoServiceInCertificateRpcAdapter;

    @AutoGenerated(locked = true, uuid = "31ea53d3-0ed9-38e2-8d89-42d44512c5a8")
    @Override
    public List<CertificateDetailDto> getByOutpVisitIds(List<String> outpVisitId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(outpVisitId)) {
            return Collections.emptyList();
        }

        List<Certificate> certificateList = certificateDao.getByOutpVisitIds(outpVisitId);
        if (CollectionUtil.isEmpty(certificateList)) {
            return Collections.emptyList();
        }

        return doConvertFromCertificateToCertificateDetailDto(certificateList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "5bb0db53-a787-3e24-bdda-1b4a33db93c2")
    @Override
    public List<CertificateDetailDto> getByPatientIds(List<String> patientId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(patientId)) {
            return Collections.emptyList();
        }

        List<Certificate> certificateList = certificateDao.getByPatientIds(patientId);
        if (CollectionUtil.isEmpty(certificateList)) {
            return Collections.emptyList();
        }

        return doConvertFromCertificateToCertificateDetailDto(certificateList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "8527f8df-13fc-369a-af24-c0db96599a73")
    public List<CertificateDetailDto> doConvertFromCertificateToCertificateDetailDto(
            List<Certificate> certificateList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(certificateList)) {
            return Collections.emptyList();
        }

        Map<String, String> patientIdMap =
                certificateList.stream()
                        .filter(i -> i.getPatientId() != null)
                        .collect(Collectors.toMap(Certificate::getId, Certificate::getPatientId));
        List<PatientBaseDto> patientIdPatientBaseDtoList =
                patientBaseDtoServiceInCertificateRpcAdapter.getByIds(
                        new ArrayList<>(new HashSet<>(patientIdMap.values())));
        Map<String, PatientBaseDto> patientIdPatientBaseDtoMapRaw =
                patientIdPatientBaseDtoList.stream()
                        .collect(Collectors.toMap(PatientBaseDto::getId, i -> i));
        Map<String, PatientBaseDto> patientIdPatientBaseDtoMap =
                patientIdMap.entrySet().stream()
                        .filter(i -> patientIdPatientBaseDtoMapRaw.get(i.getValue()) != null)
                        .collect(
                                Collectors.toMap(
                                        i -> i.getKey(),
                                        i -> patientIdPatientBaseDtoMapRaw.get(i.getValue())));
        Map<String, String> outpVisitIdMap =
                certificateList.stream()
                        .filter(i -> i.getOutpVisitId() != null)
                        .collect(Collectors.toMap(Certificate::getId, Certificate::getOutpVisitId));
        List<OutpVisitBaseDto> outpVisitIdOutpVisitBaseDtoList =
                outpVisitBaseDtoServiceInCertificateRpcAdapter.getByIds(
                        new ArrayList<>(new HashSet<>(outpVisitIdMap.values())));
        Map<String, OutpVisitBaseDto> outpVisitIdOutpVisitBaseDtoMapRaw =
                outpVisitIdOutpVisitBaseDtoList.stream()
                        .collect(Collectors.toMap(OutpVisitBaseDto::getId, i -> i));
        Map<String, OutpVisitBaseDto> outpVisitIdOutpVisitBaseDtoMap =
                outpVisitIdMap.entrySet().stream()
                        .filter(i -> outpVisitIdOutpVisitBaseDtoMapRaw.get(i.getValue()) != null)
                        .collect(
                                Collectors.toMap(
                                        i -> i.getKey(),
                                        i -> outpVisitIdOutpVisitBaseDtoMapRaw.get(i.getValue())));

        List<CertificateBaseDto> baseDtoList =
                certificateBaseDtoConverter.convertFromCertificateToCertificateBaseDto(
                        certificateList);
        Map<String, CertificateDetailDto> dtoMap =
                certificateDetailDtoConverter
                        .convertFromCertificateBaseDtoToCertificateDetailDto(baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        CertificateDetailDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<CertificateDetailDto> certificateDetailDtoList = new ArrayList<>();
        for (Certificate i : certificateList) {
            CertificateDetailDto certificateDetailDto = dtoMap.get(i.getId());
            if (certificateDetailDto == null) {
                continue;
            }

            if (null != i.getPatientId()) {
                certificateDetailDto.setPatient(
                        patientIdPatientBaseDtoMap.getOrDefault(i.getId(), null));
            }
            if (null != i.getOutpVisitId()) {
                certificateDetailDto.setOutpVisit(
                        outpVisitIdOutpVisitBaseDtoMap.getOrDefault(i.getId(), null));
            }
            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            certificateDetailDtoList.add(certificateDetailDto);
        }
        return certificateDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "983a3f1d-d57e-326f-9158-11ed4cacea51")
    @Override
    public CertificateDetailDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<CertificateDetailDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        CertificateDetailDto certificateDetailDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return certificateDetailDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "b179589a-4f44-3c02-b0aa-deaf9f3488be")
    @Override
    public List<CertificateDetailDto> getByOutpVisitId(String outpVisitId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<CertificateDetailDto> certificateDetailDtoList =
                getByOutpVisitIds(Arrays.asList(outpVisitId));
        return certificateDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "ba0aaf29-cb6a-3f12-9a75-13deee70e6e1")
    @Override
    public List<CertificateDetailDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<Certificate> certificateList = certificateDao.getByIds(id);
        if (CollectionUtil.isEmpty(certificateList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, Certificate> certificateMap =
                certificateList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        certificateList =
                id.stream()
                        .map(i -> certificateMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromCertificateToCertificateDetailDto(certificateList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "d4c46e64-0ae3-3b79-a579-d179939f75d1")
    @Override
    public List<CertificateDetailDto> getByPatientId(String patientId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<CertificateDetailDto> certificateDetailDtoList =
                getByPatientIds(Arrays.asList(patientId));
        return certificateDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
