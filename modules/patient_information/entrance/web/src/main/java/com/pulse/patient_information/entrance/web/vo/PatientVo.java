package com.pulse.patient_information.entrance.web.vo;

import com.pulse.patient_information.common.enums.PatientStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "8d5d6137-62ae-4432-bd49-029a3a205882|VO|DEFINITION")
public class PatientVo {
    /** 患者头像 */
    @AutoGenerated(locked = true, uuid = "bd3b4c82-7198-4d0e-aeef-609dd8ab0e72")
    private String avatar;

    /** 出生地 */
    @AutoGenerated(locked = true, uuid = "a5d99a30-4203-4132-bf8b-c311e5f29725")
    private String birthAddress;

    /** 出生日期 */
    @AutoGenerated(locked = true, uuid = "417ba8e0-a3f1-40bc-a695-f4edd91f1cda")
    private Date birthday;

    /** 献血证标识 */
    @AutoGenerated(locked = true, uuid = "670f73a0-9159-482e-94c2-723969bc148a")
    private Boolean bloodCardFlag;

    /** 手机号 */
    @AutoGenerated(locked = true, uuid = "2f7f0db5-c560-4401-a5ed-787ab059ecd5")
    private String cellphone;

    /** 子女统筹标志 */
    @AutoGenerated(locked = true, uuid = "04ae114f-48b2-4157-8441-4f6ed05a98a4")
    private Boolean childrenCoordinatedFlag;

    /** 子女统筹有效期 */
    @AutoGenerated(locked = true, uuid = "9fd609b8-efb2-4067-9697-326ce8d2b587")
    private Date childrenCoordinatedValidDate;

    /** 商保标志 */
    @AutoGenerated(locked = true, uuid = "3aa27491-be8c-4347-a159-af71d24dd4f6")
    private Boolean commercialInsuranceFlag;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "8db2783f-8e1b-4e5b-bad2-1ad168573176")
    private Date createdAt;

    /** 创建者 */
    @AutoGenerated(locked = true, uuid = "2e0fc148-6ee4-4277-aac6-77548d0c19fd")
    private String createdBy;

    /** 默认费别 */
    @AutoGenerated(locked = true, uuid = "24b52162-6292-400b-824a-00524489cd66")
    private String defaultChargeType;

    /** 残疾人证标识 */
    @AutoGenerated(locked = true, uuid = "58a6d40c-a58d-4f01-b341-8ceb6b3c2af6")
    private Boolean disabilityFlag;

    /** 用于显示的id */
    @AutoGenerated(locked = true, uuid = "23ba9948-747b-47c7-ad45-f3d7d227aec6")
    private String displayId;

    /** 生理性别 */
    @AutoGenerated(locked = true, uuid = "b315988c-0577-41c3-a5c0-6f767fe375d1")
    private String gender;

    /** 绿色通道标志 */
    @AutoGenerated(locked = true, uuid = "588f456d-2d7f-4c6f-b741-0568510b0ffb")
    private Boolean greenChannelFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "07bed7be-fce7-4718-a334-e582b56a8a18")
    private String id;

    /** 主证件号 */
    @AutoGenerated(locked = true, uuid = "5621daac-707f-4e23-88f4-db94f7503824")
    private String idNumber;

    /** 主证件类型 */
    @AutoGenerated(locked = true, uuid = "8f7c5a9e-f30a-48f3-b595-b7b414519395")
    private String idType;

    /** 人员类别编码 */
    @AutoGenerated(locked = true, uuid = "165c8ea8-9f56-4ebf-985f-************")
    private String identityCode;

    /** 医保卡号 */
    @AutoGenerated(locked = true, uuid = "6d13b8f2-86a6-47ce-8e96-26ed26bfde11")
    private String insuranceCardNumber;

    /** 医保账号 */
    @AutoGenerated(locked = true, uuid = "cf7b6142-1991-4e48-95a1-5326d95800dc")
    private String insuranceNumber;

    /** 医保类型 */
    @AutoGenerated(locked = true, uuid = "68142a19-57b8-4e2b-9bdd-411fa0b454d1")
    private String insuranceTypeId;

    /** 主账标志 */
    @AutoGenerated(locked = true, uuid = "38a99848-cae5-4c0e-9a16-bd04611c5c91")
    private Boolean mainAccountFlag;

    /** 劳模标志 */
    @AutoGenerated(locked = true, uuid = "943ab124-3e35-48d3-8fb5-a1ec4476296c")
    private Boolean modelWorkerFlag;

    /** 姓名 */
    @AutoGenerated(locked = true, uuid = "fb1d5ca3-8de9-403b-995f-97c794f4a70e")
    private String name;

    /** 姓名输入码 */
    @AutoGenerated(locked = true, uuid = "********-2741-4833-a6ab-990c0a27712a")
    private String nameInputCode;

    /** 联系电话 */
    @AutoGenerated(locked = true, uuid = "b6034ab6-8471-4db6-81ec-3aee3d723d63")
    private String phoneNumber;

    /** 公费级别 */
    @AutoGenerated(locked = true, uuid = "0ed15b65-7c99-479e-87ce-cd257c363ce2")
    private Long publicFundedLevel;

    /** 公费单位 */
    @AutoGenerated(locked = true, uuid = "eb592e0d-2c87-4319-94d4-f4d51196a667")
    private String publicFundedUnit;

    /** 公费证号 */
    @AutoGenerated(locked = true, uuid = "e3efa3cd-4f16-4887-90ae-805aefe3ad55")
    private String publicMedicalExpensesCertificateNumber;

    /** 状态码 */
    @AutoGenerated(locked = true, uuid = "85855cfd-adeb-46a8-b579-4c814f0716a4")
    private PatientStatusEnum status;

    /** 译名 */
    @AutoGenerated(locked = true, uuid = "1b309245-0920-4d60-b90f-3216147680bb")
    private String translateName;

    /** 无名患者标识 */
    @AutoGenerated(locked = true, uuid = "f0b39661-6c01-47b9-b2d4-f3ceb1fa1747")
    private Boolean unknownFlag;

    /** 无名患者类型 */
    @AutoGenerated(locked = true, uuid = "7b67bb87-372a-4e70-b897-2308e216912a")
    private String unknownType;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "1e99ce3b-57e6-43bc-8e32-66dae65a8fce")
    private Date updatedAt;

    /** 操作员 */
    @AutoGenerated(locked = true, uuid = "44b71205-6315-409c-964c-25ac463051e6")
    private String updatedBy;

    /** 退伍军人标志 */
    @AutoGenerated(locked = true, uuid = "6c235314-ec9e-4e6a-9736-310d2ba3ef51")
    private Boolean veteranFlag;

    /** VIP标识 */
    @AutoGenerated(locked = true, uuid = "38f8f049-0b02-4c5c-972a-55cc744f4faf")
    private Boolean vipFlag;
}
