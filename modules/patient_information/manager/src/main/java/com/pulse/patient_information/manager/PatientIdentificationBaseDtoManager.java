package com.pulse.patient_information.manager;

import com.pulse.patient_information.manager.dto.PatientIdentificationBaseDto;
import com.pulse.patient_information.persist.dos.PatientIdentification;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "cfc72530-554f-4e8b-aee9-342e33244626|DTO|MANAGER")
public interface PatientIdentificationBaseDtoManager {

    @AutoGenerated(locked = true, uuid = "2043e150-04be-3ecf-85ba-d9ffc7a57d22")
    List<PatientIdentificationBaseDto> getByIdentificationClasssAndIdentificationNumbers(
            List<PatientIdentification.IdentificationClassAndIdentificationNumber> var);

    @AutoGenerated(locked = true, uuid = "33c608e3-a138-3c84-8868-6f506a806e6e")
    PatientIdentificationBaseDto getByIdentificationClassAndIdentificationNumber(
            PatientIdentification.IdentificationClassAndIdentificationNumber var);

    @AutoGenerated(locked = true, uuid = "5fe7ada6-b65b-380f-91f9-62325238b252")
    List<PatientIdentificationBaseDto> getByPatientId(String patientId);

    @AutoGenerated(locked = true, uuid = "8bf50aa7-ceb3-3917-93f5-9c8aa87cfc1c")
    PatientIdentificationBaseDto getById(String id);

    @AutoGenerated(locked = true, uuid = "e3b2cdef-8b65-3b1a-9fc5-db3b8296d3cc")
    List<PatientIdentificationBaseDto> getByPatientIds(List<String> patientId);

    @AutoGenerated(locked = true, uuid = "fd9efe5c-c0a0-33da-9969-fd1e2f23b3e4")
    List<PatientIdentificationBaseDto> getByIds(List<String> id);
}
