package com.pulse.billing_public_config.entrance.web.query.collector;

import com.pulse.billing_public_config.entrance.web.converter.ChargeItemCommonVoConverter;
import com.pulse.billing_public_config.entrance.web.converter.ChargeItemPriceDetailChargeItemExtVoConverter;
import com.pulse.billing_public_config.entrance.web.query.assembler.ChargeItemPriceDetailChargeItemExtVoDataAssembler.ChargeItemPriceDetailChargeItemExtVoDataHolder;
import com.pulse.billing_public_config.entrance.web.vo.ChargeItemCommonVo;
import com.pulse.billing_public_config.manager.dto.ChargeItemBaseDto;
import com.pulse.billing_public_config.manager.dto.ChargeItemPriceDetailBaseDto;
import com.pulse.billing_public_config.manager.dto.ChargeItemPriceDetailChargeItemExtDto;
import com.pulse.billing_public_config.service.ChargeItemBaseDtoService;
import com.pulse.billing_public_config.service.ChargeItemPriceDetailBaseDtoService;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装ChargeItemPriceDetailChargeItemExtVo所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "b21c54fb-945d-35e0-8327-5744ff09e433")
public class ChargeItemPriceDetailChargeItemExtVoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemBaseDtoService chargeItemBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemCommonVoConverter chargeItemCommonVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemPriceDetailBaseDtoService chargeItemPriceDetailBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemPriceDetailChargeItemExtVoConverter
            chargeItemPriceDetailChargeItemExtVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ChargeItemPriceDetailChargeItemExtVoDataCollector
            chargeItemPriceDetailChargeItemExtVoDataCollector;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "02de72b9-1022-30fc-9ed4-be927ab26076")
    private void fillDataWhenNecessary(ChargeItemPriceDetailChargeItemExtVoDataHolder dataHolder) {
        List<ChargeItemPriceDetailBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.chargeItem == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(ChargeItemPriceDetailBaseDto::getChargeItemCode)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<ChargeItemBaseDto> baseDtoList =
                    chargeItemBaseDtoService.getByItemCodes(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(ChargeItemBaseDto::getItemCode))
                            .collect(Collectors.toList());
            Map<String, ChargeItemBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            ChargeItemBaseDto::getItemCode, Function.identity()));
            Map<ChargeItemBaseDto, ChargeItemCommonVo> dtoVoMap =
                    chargeItemCommonVoConverter.convertToChargeItemCommonVoMap(baseDtoList);
            Map<ChargeItemBaseDto, ChargeItemCommonVo> baseDtoVoMap =
                    baseDtoList.stream()
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> dtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
            dataHolder.chargeItem =
                    rootDtoList.stream()
                            .map(ChargeItemPriceDetailBaseDto::getChargeItemCode)
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(
                                            Function.identity(),
                                            baseDto -> baseDtoVoMap.get(baseDto),
                                            (o1, o2) -> o1,
                                            LinkedHashMap::new));
        }
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "4f8d8836-d2fd-3fa6-9a1c-3637af829d9f")
    public void collectDataDefault(ChargeItemPriceDetailChargeItemExtVoDataHolder dataHolder) {
        chargeItemPriceDetailChargeItemExtVoDataCollector.fillDataWhenNecessary(dataHolder);
    }

    /**
     * 获取ChargeItemPriceDetailChargeItemExtDto数据填充ChargeItemPriceDetailChargeItemExtVo，并根据扩展关系填充剩余数据
     */
    @AutoGenerated(locked = true, uuid = "572192ff-c75b-386c-890a-29a0158ab72c")
    public void collectDataWithDtoData(
            List<ChargeItemPriceDetailChargeItemExtDto> dtoList,
            ChargeItemPriceDetailChargeItemExtVoDataHolder dataHolder) {
        List<ChargeItemBaseDto> chargeItemList = new ArrayList<>();

        for (ChargeItemPriceDetailChargeItemExtDto rootDto : dtoList) {
            ChargeItemBaseDto chargeItemDto = rootDto.getChargeItem();
            if (chargeItemDto != null) {
                chargeItemList.add(chargeItemDto);
            }
        }

        // access chargeItem
        Map<ChargeItemBaseDto, ChargeItemCommonVo> chargeItemVoMap =
                chargeItemCommonVoConverter.convertToChargeItemCommonVoMap(chargeItemList);
        dataHolder.chargeItem =
                chargeItemList.stream()
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        baseDto -> chargeItemVoMap.get(baseDto),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));

        fillDataWhenNecessary(dataHolder);
    }
}
