package com.pulse.organization.entrance.web.vo;

import com.pulse.application.common.enums.ApplicationStatusEnum;
import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "e9be1b98-10f8-4860-a16e-83d9f5fc068c|VO|DEFINITION")
public class OrganizationRefApplicationBaseVo {
    /** 缩写 */
    @AutoGenerated(locked = true, uuid = "cdf92e4a-5c64-4432-b7f5-8a5528b77f35")
    private String abbreviation;

    /** 分类ID */
    @AutoGenerated(locked = true, uuid = "c501cacd-230d-4a49-9c72-23e4b76d8c09")
    private String categoryId;

    /** 应用编码 */
    @AutoGenerated(locked = true, uuid = "5d93f267-670a-49c8-97bc-2ec8a5844aae")
    private String code;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "632602a7-d5b9-4e20-bf51-caf4cdc6dd41")
    private Date createdAt;

    /** 描述 */
    @AutoGenerated(locked = true, uuid = "276337af-1b08-41ec-842b-a7836fcf4e10")
    private String description;

    /** 状态 */
    @AutoGenerated(locked = true, uuid = "9619c87c-2efb-4e8a-941b-c6fe6166fc07")
    private Boolean enableFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "cb8ca8af-683d-4356-87ef-c524ae54e065")
    private String id;

    /** 输入码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "89ad5921-eb43-4520-92f2-ba281bd66b09")
    private InputCodeEo inputCode;

    /** 多实例标识 */
    @AutoGenerated(locked = true, uuid = "9d986d83-825f-4c01-b824-71f7c8d5992e")
    private Boolean multipleInstanceFlag;

    /** 名称 */
    @AutoGenerated(locked = true, uuid = "d3d5ac15-711e-4ad1-a505-c8f175477b59")
    private String name;

    /** 所属组织ID */
    @AutoGenerated(locked = true, uuid = "c7e94931-b844-4e49-9f6a-3f8b2a57a1e5")
    private String organizationId;

    /** 父应用ID */
    @AutoGenerated(locked = true, uuid = "1e139ac4-9413-4c2f-8551-340e6ccddd28")
    private String parentApplicationId;

    /** 路由ID */
    @AutoGenerated(locked = true, uuid = "13a885ff-fd31-4609-9407-435af3c05b91")
    private String routerId;

    /** 状态 */
    @AutoGenerated(locked = true, uuid = "9adaca2e-7411-4048-baf8-6772cec7d88a")
    private ApplicationStatusEnum status;

    /** 模板应用标识 */
    @AutoGenerated(locked = true, uuid = "60243d09-4c93-427f-b535-9a380139f7ad")
    private Boolean templateApplicationFlag;

    /** 模板应用ID */
    @AutoGenerated(locked = true, uuid = "3e3c91a1-2b85-4b5d-92a3-28249ab7251a")
    private String templateApplicationId;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "35ea1494-a2f9-4f70-9783-8377ccdee7f5")
    private Date updatedAt;
}
