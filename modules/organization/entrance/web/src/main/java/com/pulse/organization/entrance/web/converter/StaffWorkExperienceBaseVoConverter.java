package com.pulse.organization.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.organization.entrance.web.query.assembler.StaffWorkExperienceBaseVoDataAssembler;
import com.pulse.organization.entrance.web.vo.StaffWorkExperienceBaseVo;
import com.pulse.organization.manager.dto.StaffWorkExperienceBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到StaffWorkExperienceBaseVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "e2a8b7e1-c718-4427-ab4b-3f637e2c3d77|VO|CONVERTER")
public class StaffWorkExperienceBaseVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private StaffWorkExperienceBaseVoDataAssembler staffWorkExperienceBaseVoDataAssembler;

    /** 把StaffWorkExperienceBaseDto转换成StaffWorkExperienceBaseVo */
    @AutoGenerated(locked = true, uuid = "761a9eea-69f6-35bd-9c46-f74de063f7d4")
    public StaffWorkExperienceBaseVo convertToStaffWorkExperienceBaseVo(
            StaffWorkExperienceBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToStaffWorkExperienceBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装StaffWorkExperienceBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "7e0f14f9-07c4-33a5-91cc-19387cc2dced")
    public StaffWorkExperienceBaseVo convertAndAssembleData(StaffWorkExperienceBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装StaffWorkExperienceBaseVo列表数据 */
    @AutoGenerated(locked = true, uuid = "c1f14177-22d9-36b1-bc07-7664325c8ea7")
    public List<StaffWorkExperienceBaseVo> convertAndAssembleDataList(
            List<StaffWorkExperienceBaseDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        Map<String, StaffWorkExperienceBaseVo> voMap =
                convertToStaffWorkExperienceBaseVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        staffWorkExperienceBaseVoDataAssembler.assembleData(voMap);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把StaffWorkExperienceBaseDto转换成StaffWorkExperienceBaseVo */
    @AutoGenerated(locked = false, uuid = "e2a8b7e1-c718-4427-ab4b-3f637e2c3d77-converter-Map")
    public Map<StaffWorkExperienceBaseDto, StaffWorkExperienceBaseVo>
            convertToStaffWorkExperienceBaseVoMap(List<StaffWorkExperienceBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<StaffWorkExperienceBaseDto, StaffWorkExperienceBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            StaffWorkExperienceBaseVo vo =
                                                    new StaffWorkExperienceBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setStaffId(dto.getStaffId());
                                            vo.setStartDate(dto.getStartDate());
                                            vo.setEndDate(dto.getEndDate());
                                            vo.setWorkUnit(dto.getWorkUnit());
                                            vo.setPosition(dto.getPosition());
                                            vo.setBelongToDepartment(dto.getBelongToDepartment());
                                            vo.setAccountingDepartment(
                                                    dto.getAccountingDepartment());
                                            vo.setHrDepartment(dto.getHrDepartment());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把StaffWorkExperienceBaseDto转换成StaffWorkExperienceBaseVo */
    @AutoGenerated(locked = true, uuid = "e2a8b7e1-c718-4427-ab4b-3f637e2c3d77-converter-list")
    public List<StaffWorkExperienceBaseVo> convertToStaffWorkExperienceBaseVoList(
            List<StaffWorkExperienceBaseDto> dtoList) {
        return new ArrayList<>(convertToStaffWorkExperienceBaseVoMap(dtoList).values());
    }
}
