package com.pulse.organization.entrance.web.vo;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "23450689-f645-4522-9c40-f30e5f60eaf0|VO|DEFINITION")
public class StaffPracticeBaseVo {
    /** 麻醉医生编号 */
    @AutoGenerated(locked = true, uuid = "763172aa-f644-4524-aa55-dba4dcbcb26a")
    private String anesthesiologistNumber;

    /** 执业类别 */
    @AutoGenerated(locked = true, uuid = "08763b7a-b764-4ce4-9468-63c4f258d990")
    private String category;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "01d2eb30-0f64-4441-b3a8-2118b4cd20db")
    private Date createdAt;

    /** 医生医保编号 */
    @AutoGenerated(locked = true, uuid = "a02c110b-9444-4a01-b5e1-bbb6109783de")
    private String doctorInsuranceId;

    /** 结束日期 */
    @AutoGenerated(locked = true, uuid = "0a710b9f-2439-4504-8ca5-d88c3e7fd907")
    private Date endDate;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "95346d61-67f8-4d4a-b671-67cdc67618fe")
    private String id;

    /** 执业证书编号 */
    @AutoGenerated(locked = true, uuid = "484d00c6-26ff-47dc-a848-70afb9185e5f")
    private String practiceCertificateNumber;

    /** 执业科别 */
    @AutoGenerated(locked = true, uuid = "5b3565f2-7b42-47ff-a99c-00095650de78")
    private String practiceDepartment;

    /** 范围 */
    @AutoGenerated(locked = true, uuid = "96ab1bee-03a3-409d-adc5-2cd1dbede367")
    private String practiceScope;

    /** 资格证书编号 */
    @AutoGenerated(locked = true, uuid = "d2d0e155-bfaa-44f7-87a0-e2da85d02f66")
    private String qualificationCertificateNumber;

    /** 放射科医生编号 */
    @AutoGenerated(locked = true, uuid = "e16a5665-0173-4be5-b5fa-1cd91559cbf7")
    private String radiologistNumber;

    /** 员工ID */
    @AutoGenerated(locked = true, uuid = "5d737e10-962c-4dff-9a02-bc1616a60e0e")
    private String staffId;

    /** 开始日期 */
    @AutoGenerated(locked = true, uuid = "7a0baba2-5c73-4f1a-aee0-1ff017e054e5")
    private Date startDate;

    /** 技术职称 */
    @AutoGenerated(locked = true, uuid = "2c31a9d4-1739-4186-8cc3-b4370d2f660f")
    private String technicalTitle;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "8f515892-b227-4dd2-87d3-f8ec6254a4e3")
    private Date updatedAt;
}
