package com.pulse.organization.manager.dto;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.organization.common.enums.OrganizationStatusEnum;
import com.pulse.organization.common.enums.OrganizationTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "083e5efe-91f4-4074-90ec-238e22ff8c9a|DTO|DEFINITION")
public class OrganizationBaseDto {
    /** 简称 */
    @AutoGenerated(locked = true, uuid = "3c8995c4-1b18-4e93-90d1-9f61235402a7")
    private String abbreviation;

    /** 组织地址 */
    @AutoGenerated(locked = true, uuid = "e5d4cac4-**************-b5b7381bb5df")
    private String address;

    /** 别名 */
    @AutoGenerated(locked = true, uuid = "8ef49b2a-6af3-4e7a-afb9-338875d23ce7")
    private String alias;

    /** 联系电话 */
    @AutoGenerated(locked = true, uuid = "57ae8f3b-6575-45f2-b8f2-0db8ac8d1db6")
    private String contactNumber;

    /** 联系人 */
    @AutoGenerated(locked = true, uuid = "34da5e07-c10b-4dd0-a330-5a92deea0251")
    private String contactPerson;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "9ee6135d-7b4b-452b-a28a-cf4fb9311e2b")
    private Date createdAt;

    /** 创建者ID */
    @AutoGenerated(locked = true, uuid = "462f2c84-9b18-4d18-9462-0bb25f1af4b2")
    private String createdBy;

    /** 删除时间 */
    @AutoGenerated(locked = true, uuid = "2be7fa60-caf1-4ad3-932b-f3541323649f")
    private Long deletedAt;

    /** 删除者ID */
    @AutoGenerated(locked = true, uuid = "51b510a5-e580-49a4-b147-3c912f2dcee6")
    private String deletedBy;

    /** 组织描述 */
    @AutoGenerated(locked = true, uuid = "23a04008-5b90-4a12-8470-0b2d7335b2a8")
    private String description;

    /** 英文名 */
    @AutoGenerated(locked = true, uuid = "8ea6b0b2-5947-484c-a75c-138cc6ecbe90")
    private String englishName;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "e9a5dc8f-ec6c-43cc-97aa-746e6637b49c")
    private String id;

    /** 输入代码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "217fd315-a330-4955-a11a-54f85940a866")
    private InputCodeEo inputCode;

    /** 作废标记 */
    @AutoGenerated(locked = true, uuid = "f6ba647b-2840-4ddd-941e-3b3fda5b2f84")
    private Boolean invalidFlag;

    /** 乐观锁字段 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "d02ca536-**************-ab881e570b62")
    private Long lockVersion;

    /** 组织名称 */
    @AutoGenerated(locked = true, uuid = "4f79c56e-b082-4874-b4b9-6ae4587dbad4")
    private String name;

    /** 组织层级 */
    @AutoGenerated(locked = true, uuid = "dac2535e-df07-43e5-a63e-3d08ba98bfe2")
    private Long organizationLevel;

    /** 上级组织ID */
    @AutoGenerated(locked = true, uuid = "49491871-085d-4570-a299-d8d9a2ee82fe")
    private String parentId;

    /** 排序号 */
    @AutoGenerated(locked = true, uuid = "2e97b142-f807-478d-8024-56550309534b")
    private Long sortNumber;

    /** 组织状态 */
    @AutoGenerated(locked = true, uuid = "e796f875-8a13-4828-84b4-bbfb871a4bbb")
    private OrganizationStatusEnum status;

    /** 组织类型 */
    @AutoGenerated(locked = true, uuid = "e68ec60a-57e6-4892-861f-4322a1695e53")
    private OrganizationTypeEnum type;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "4fde3088-7efe-4225-a168-ff3801fe07c6")
    private Date updatedAt;

    /** 更新者ID */
    @AutoGenerated(locked = true, uuid = "05505baa-81be-486a-9c57-7030ba3466be")
    private String updatedBy;
}
