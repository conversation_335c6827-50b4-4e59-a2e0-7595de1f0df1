package com.pulse.application.entrance.web.vo;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "a3f6387d-3421-43a3-89f7-98052b072aab|VO|DEFINITION")
public class ApplicationOrganizationMenuVo {
    /** 应用ID */
    @Valid
    @AutoGenerated(locked = true, uuid = "5f471c6c-8c9b-4991-9584-6ba6679b1d6f")
    private ApplicationMenuFullVo application;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "1e1b5fe2-2208-4a30-a8bd-9ab982841326")
    private Date createdAt;

    /** 描述 */
    @AutoGenerated(locked = true, uuid = "0ca8b485-f716-4fcf-9931-29167c6f5889")
    private String description;

    /** 急诊 */
    @AutoGenerated(locked = true, uuid = "f83c37a0-0d13-432f-82ef-b9ef044d09b2")
    private Boolean emergencyFlag;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "19f7e957-6323-441a-83aa-333a78a2fa66")
    private String id;

    /** 输入码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "162c4fe7-585a-460a-9fab-97e70460fce6")
    private InputCodeEo inputCode;

    /** 库存 */
    @AutoGenerated(locked = true, uuid = "4de5a8bf-f2c1-435c-820c-45924c3a3c54")
    private String inventory;

    /** 组织ID */
    @AutoGenerated(locked = true, uuid = "8f9a8424-bf3e-4ebb-a3ca-73a61af50a55")
    private String organizationId;

    /** 门诊费用 */
    @AutoGenerated(locked = true, uuid = "a5750835-6290-478b-b43a-a4829c688e9a")
    private Boolean outpatientFeeFlag;

    /** 药房标志 */
    @AutoGenerated(locked = true, uuid = "77e7dac7-6a67-43ea-9eeb-262d8f5a5c5d")
    private Boolean pharmacyFlag;

    /** 应用简称 */
    @AutoGenerated(locked = true, uuid = "1520d863-6ee2-46c9-aa28-b285e16875f5")
    private String shortName;

    /** 排序编号 */
    @AutoGenerated(locked = true, uuid = "9c943881-e90e-4161-8252-6776ced6577a")
    private Integer sortNumber;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "5d032f90-498f-492d-85b5-15763b0271a7")
    private Date updatedAt;
}
