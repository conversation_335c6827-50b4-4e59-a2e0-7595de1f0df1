package com.pulse.dictionary_business.service.bto;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.dictionary_business.common.enums.ExamTypeBillingModeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> ExamDevice
 *
 * <p><b>[操作]</b> UPDATE_ON_MISS_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "fb21da45-b038-4387-bd3f-df5800b5f9e0|BTO|DEFINITION")
public class UpdateExamDeviceBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 计费模式 */
    @AutoGenerated(locked = true, uuid = "5716fe3a-d11f-47be-884d-1d9a9c490fec")
    private ExamTypeBillingModeEnum billingMode;

    /** 院区id */
    @Valid
    @AutoGenerated(locked = true, uuid = "d818adec-9e0a-4aee-b68b-eca52c7b933c")
    private List<String> campusIdList;

    /** 设备ID */
    @AutoGenerated(locked = true, uuid = "f181ec11-dad3-4cf3-a714-4bb654cb8a69")
    private String deviceId;

    /** 设备名称 */
    @AutoGenerated(locked = true, uuid = "dd034fef-0852-425f-9b01-669019b04b2c")
    private String deviceName;

    /** 检查类型id */
    @AutoGenerated(locked = true, uuid = "e63e4ecb-221f-4d47-8186-0343d934fd36")
    private String examTypeId;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "29b46c17-8a52-4014-9e3b-db43ec415fca")
    private String id;

    /** 输入代码 */
    @Valid
    @AutoGenerated(locked = true, uuid = "a0ff399e-aa25-4232-b71d-f215c976be6d")
    private InputCodeEo inputCode;

    /** 排序编号 */
    @AutoGenerated(locked = true, uuid = "ac32a103-6101-400d-a0ab-6960e8b27acb")
    private Long sortNumber;

    /** 更新者 */
    @AutoGenerated(locked = true, uuid = "18bd2a36-293d-47a9-9caf-3acda106c427")
    private String updatedBy;

    @AutoGenerated(locked = true)
    public void setBillingMode(ExamTypeBillingModeEnum billingMode) {
        this.__$validPropertySet.add("billingMode");
        this.billingMode = billingMode;
    }

    @AutoGenerated(locked = true)
    public void setCampusId(List<String> campusId) {
        this.__$validPropertySet.add("campusIdList");
        this.campusIdList = campusId;
    }

    @AutoGenerated(locked = true)
    public void setCampusIdList(List<String> campusIdList) {
        this.__$validPropertySet.add("campusIdList");
        this.campusIdList = campusIdList;
    }

    @AutoGenerated(locked = true)
    public void setDeviceId(String deviceId) {
        this.__$validPropertySet.add("deviceId");
        this.deviceId = deviceId;
    }

    @AutoGenerated(locked = true)
    public void setDeviceName(String deviceName) {
        this.__$validPropertySet.add("deviceName");
        this.deviceName = deviceName;
    }

    @AutoGenerated(locked = true)
    public void setExamTypeId(String examTypeId) {
        this.__$validPropertySet.add("examTypeId");
        this.examTypeId = examTypeId;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setInputCode(InputCodeEo inputCode) {
        this.__$validPropertySet.add("inputCode");
        this.inputCode = inputCode;
    }

    @AutoGenerated(locked = true)
    public void setSortNumber(Long sortNumber) {
        this.__$validPropertySet.add("sortNumber");
        this.sortNumber = sortNumber;
    }

    @AutoGenerated(locked = true)
    public void setUpdatedBy(String updatedBy) {
        this.__$validPropertySet.add("updatedBy");
        this.updatedBy = updatedBy;
    }
}
