package com.pulse.dictionary_business.entrance.web.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;

import com.pulse.dictionary_basic.service.bto.MergeAttributeValueBto;
import com.pulse.dictionary_business.service.ClinicItemDictionaryBOService;
import com.pulse.dictionary_business.service.LabItemBOService;
import com.pulse.dictionary_business.service.bto.*;
import com.pulse.dictionary_business.service.bto.CreateClinicItemChargeItemBto;
import com.pulse.dictionary_business.service.bto.CreateClinicItemGuideDescriptionBto;
import com.pulse.dictionary_business.service.bto.CreateLabItemBto;
import com.pulse.dictionary_business.service.bto.SaveLabItemDetailBto;
import com.pulse.dictionary_business.service.bto.SaveLabItemPerformDepartmentBto;
import com.pulse.dictionary_business.service.bto.SaveLabItemVsSpecimenBto;
import com.pulse.dictionary_business.service.bto.UpdateLabItemBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "38901aef-273f-3fc7-a0cf-8bf1689efc7a")
public class LabItemBOController {
    @AutoGenerated(locked = true)
    @Resource
    private LabItemBOService labItemBOService;

    @Resource private ClinicItemDictionaryBOService clinicItemDictionaryBOService;

    /** 修改检验信息 */
    @PublicInterface(id = "10df742f-6cc0-40d7-9967-4815ca7fb232", version = "1741159480184")
    @AutoGenerated(locked = false, uuid = "10df742f-6cc0-40d7-9967-4815ca7fb232")
    @RequestMapping(
            value = {"/api/dictionary-business/update-lab-item"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    @Transactional
    public String updateLabItem(@Valid @NotNull UpdateLabItemBto updateLabItemBto) {
        UpdateClinicItemDictionaryBto updateClinicItemDictionaryBto =
                new UpdateClinicItemDictionaryBto();
        updateClinicItemDictionaryBto.setClinicItemId(updateLabItemBto.getId());
        updateClinicItemDictionaryBto.setDoubleSignatureFlag(
                updateLabItemBto.getDoubleSignatureFlag());
        clinicItemDictionaryBOService.updateClinicItemDictionary(updateClinicItemDictionaryBto);
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = labItemBOService.updateLabItem(updateLabItemBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 保存检验项目对应标本信息 */
    @PublicInterface(id = "712e8382-25e9-4b30-9f49-de00eaaa0ae4", version = "1741329971046")
    @AutoGenerated(locked = false, uuid = "712e8382-25e9-4b30-9f49-de00eaaa0ae4")
    @RequestMapping(
            value = {"/api/dictionary-business/save-lab-item-vs-specimen"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String saveLabItemVsSpecimen(@Valid SaveLabItemVsSpecimenBto saveLabItemVsSpecimenBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = labItemBOService.saveLabItemVsSpecimen(saveLabItemVsSpecimenBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 保存检验项目执行科室信息 */
    @PublicInterface(id = "94ae6d4d-02d6-4a43-9562-7d7fa49cae20", version = "1741329842832")
    @AutoGenerated(locked = false, uuid = "94ae6d4d-02d6-4a43-9562-7d7fa49cae20")
    @RequestMapping(
            value = {"/api/dictionary-business/save-lab-item-perform-department"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String saveLabItemPerformDepartment(
            @Valid SaveLabItemPerformDepartmentBto saveLabItemPerformDepartmentBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result =
                labItemBOService.saveLabItemPerformDepartment(saveLabItemPerformDepartmentBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 创建检验项目 */
    @PublicInterface(id = "ad759acb-9f95-4647-a8da-0f3ed895747a", version = "1744191986109")
    @AutoGenerated(locked = false, uuid = "ad759acb-9f95-4647-a8da-0f3ed895747a")
    @RequestMapping(
            value = {"/api/dictionary-business/create-lab-item"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    @Transactional
    public String createLabItem(
            @Valid @NotNull CreateLabItemBto createLabItemBto,
            @Valid
                    List<CreateClinicItemGuideDescriptionBto.ClinicItemGuideDescriptionBto>
                            clinicItemGuideDescriptionList,
            @Valid
                    List<CreateClinicItemChargeItemBto.ClinicItemChargeItemBto>
                            clinicItemChargeItemList,
            @Valid List<MergeAttributeValueBto> attributeValueList) {
        CreateClinicItemDictionaryBto createClinicItemDictionaryBto =
                new CreateClinicItemDictionaryBto();
        createClinicItemDictionaryBto.setAgeMaxLimit(
                createLabItemBto.getLabItemRuleBto().getLimitAgeMax());
        createClinicItemDictionaryBto.setAgeMinLimit(
                createLabItemBto.getLabItemRuleBto().getLimitAgeMin());
        createClinicItemDictionaryBto.setAuditDate(new Date());
        createClinicItemDictionaryBto.setAuditFlag(Boolean.TRUE);
        if (CollectionUtil.isNotEmpty(clinicItemChargeItemList)) {
            createClinicItemDictionaryBto.setClinicItemChargeItemBtoList(
                    BeanUtil.copyToList(
                            clinicItemChargeItemList,
                            CreateClinicItemDictionaryBto.ClinicItemChargeItemBto.class));
        }
        if (CollectionUtil.isNotEmpty(clinicItemGuideDescriptionList)) {
            createClinicItemDictionaryBto.setClinicItemGuideDescriptionBtoList(
                    BeanUtil.copyToList(
                            clinicItemGuideDescriptionList,
                            CreateClinicItemDictionaryBto.ClinicItemGuideDescriptionBto.class));
        }
        createClinicItemDictionaryBto.setClinicItemId(
                StrUtil.isBlank(createLabItemBto.getClinicItemId())
                        ? null
                        : createLabItemBto.getClinicItemId());
        createClinicItemDictionaryBto.setClinicItemName(createLabItemBto.getLabItemName());
        createClinicItemDictionaryBto.setDescription(
                createLabItemBto.getLabItemRuleBto().getLabItemInstruction());
        createClinicItemDictionaryBto.setDoubleSignatureFlag(
                createLabItemBto.getDoubleSignatureFlag());
        createClinicItemDictionaryBto.setEnableFlag(Boolean.TRUE);
        createClinicItemDictionaryBto.setInputCode(createLabItemBto.getInputCode());
        createClinicItemDictionaryBto.setItemSpecification("/");
        createClinicItemDictionaryBto.setItemType("LAB");
        createClinicItemDictionaryBto.setLimitGender(
                createLabItemBto.getLabItemRuleBto().getLimitGender());

        String clinicItemId =
                clinicItemDictionaryBOService.createClinicItemDictionary(
                        createClinicItemDictionaryBto);
        createLabItemBto.setClinicItemId(clinicItemId);
        createLabItemBto.setId(clinicItemId);
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = labItemBOService.createLabItem(createLabItemBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 保存检验套餐明细 */
    @PublicInterface(id = "d887894b-3b3f-480d-8f23-7932e653dcfd", version = "1741329634108")
    @AutoGenerated(locked = false, uuid = "d887894b-3b3f-480d-8f23-7932e653dcfd")
    @RequestMapping(
            value = {"/api/dictionary-business/save-lab-item-detail"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String saveLabItemDetail(@Valid SaveLabItemDetailBto saveLabItemDetailBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = labItemBOService.saveLabItemDetail(saveLabItemDetailBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }
}
