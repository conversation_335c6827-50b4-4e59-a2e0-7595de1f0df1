package com.pulse.appointment_booking.service.flow.node.create_add_register;

import com.pulse.appointment_booking.service.OutpRegisterBOService;
import com.pulse.appointment_booking.service.flow.context.CreateAddRegisterContext;
import com.vs.code.AutoGenerated;
import com.vs.flow.node.NodeComponent;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component("appointmentBooking-createAddRegister-saveOutpRegister")
@AutoGenerated(locked = false, uuid = "694da8eb-49c3-445b-a17c-d9aa900e8217|FLOW_NODE|DEFINITION")
public class SaveOutpRegisterNode extends NodeComponent {

    @Resource OutpRegisterBOService outpRegisterBOService;

    /**
     * 实现节点处理逻辑 节点之间传参都必须通过Context传递 如果要去取Context，调用参数 getFirstContextBean() 如果要终止流程，调用
     * super.setEnd();
     */
    @AutoGenerated(locked = false, uuid = "694da8eb-49c3-445b-a17c-d9aa900e8217")
    public void process() {
        /** This block is generated by vs, do not modify, start anchor 1 */
        /** 获取宿主流程的context */
        CreateAddRegisterContext context = getFirstContextBean();
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 以下开始处理业务逻辑
        outpRegisterBOService.createOutpRegister(context.getCreateOutpRegisterBto());
        System.out.println("save_outp_register");
    }
}
