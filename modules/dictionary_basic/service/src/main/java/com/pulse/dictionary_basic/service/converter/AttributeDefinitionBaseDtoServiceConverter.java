package com.pulse.dictionary_basic.service.converter;

import com.pulse.dictionary_basic.manager.dto.AttributeDefinitionBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "45b325d8-847f-3975-b57d-45190f741024")
public class AttributeDefinitionBaseDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<AttributeDefinitionBaseDto> AttributeDefinitionBaseDtoConverter(
            List<AttributeDefinitionBaseDto> attributeDefinitionBaseDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return attributeDefinitionBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
