package com.pulse.dictionary_basic.service.query;

import com.pulse.dictionary_basic.manager.converter.AttributeWithValueDtoConverter;
import com.pulse.dictionary_basic.manager.dto.AttributeDefinitionBaseDto;
import com.pulse.dictionary_basic.manager.dto.AttributeWithValueDto;
import com.pulse.dictionary_basic.persist.qto.SearchAttributeWithValueQto;
import com.pulse.dictionary_basic.service.AttributeDefinitionBaseDtoService;
import com.pulse.dictionary_basic.service.index.entity.SearchAttributeWithValueQtoService;
import com.pulse.dictionary_basic.service.query.assembler.AttributeWithValueDtoDataAssembler;
import com.pulse.dictionary_basic.service.query.assembler.AttributeWithValueDtoDataAssembler.AttributeWithValueDtoDataHolder;
import com.pulse.dictionary_basic.service.query.collector.AttributeWithValueDtoDataCollector;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/** AttributeWithValueDto查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "fe9a749b-db7c-366d-add1-3d19c0169873")
public class AttributeWithValueDtoQueryService {
    @AutoGenerated(locked = true)
    @Resource
    private AttributeDefinitionBaseDtoService attributeDefinitionBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private AttributeWithValueDtoConverter attributeWithValueDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private AttributeWithValueDtoDataAssembler attributeWithValueDtoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private AttributeWithValueDtoDataCollector attributeWithValueDtoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private SearchAttributeWithValueQtoService searchAttributeWithValueQtoService;

    /** 根据SearchAttributeWithValueQto查询AttributeWithValueDto列表,分页 */
    @PublicInterface(id = "3083f002-d8f2-4482-9a59-1b8fa3bd6af4", module = "dictionary_basic")
    @AutoGenerated(locked = false, uuid = "11662b2f-341c-3ea4-a5ca-5cde9a688a6f")
    public VSQueryResult<AttributeWithValueDto> searchAttributePaged(
            @Valid @NotNull SearchAttributeWithValueQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchAttributeWithValueQtoService.queryPaged(qto);
        AttributeWithValueDtoDataHolder dataHolder = new AttributeWithValueDtoDataHolder();
        List<AttributeWithValueDto> dtoList = toDtoList(ids, dataHolder);
        attributeWithValueDtoDataCollector.collectDataBySearchAttributeWithValueQto(
                dataHolder, qto);
        attributeWithValueDtoDataAssembler.assembleData(dtoList, dataHolder);
        VSQueryResult result = new VSQueryResult();
        result.setCount(searchAttributeWithValueQtoService.count(qto));
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 将ID列表转换为DtoList */
    @AutoGenerated(locked = true, uuid = "9afde7b9-c8fc-3528-a97e-bc203718b70f")
    private List<AttributeWithValueDto> toDtoList(
            List<String> ids, AttributeWithValueDtoDataHolder dataHolder) {
        List<AttributeDefinitionBaseDto> baseDtoList =
                attributeDefinitionBaseDtoService.getByIds(ids);
        dataHolder.setRootBaseDtoList(baseDtoList);
        Map<String, AttributeWithValueDto> dtoMap =
                attributeWithValueDtoConverter
                        .convertFromAttributeDefinitionBaseDtoToAttributeWithValueDto(baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        AttributeWithValueDto::getId, Function.identity()));
        return ids.stream()
                .map(id -> dtoMap.get(id))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 根据SearchAttributeWithValueQto查询AttributeWithValueDto列表,上限500 */
    @PublicInterface(id = "10dad1ea-641f-4fa4-b7c7-6b3e9da88414", module = "dictionary_basic")
    @AutoGenerated(locked = false, uuid = "b87204fd-1e82-39c3-9a2e-8700b5723617")
    public List<AttributeWithValueDto> searchAttribute(
            @Valid @NotNull SearchAttributeWithValueQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = searchAttributeWithValueQtoService.query(qto);
        AttributeWithValueDtoDataHolder dataHolder = new AttributeWithValueDtoDataHolder();
        List<AttributeWithValueDto> result = toDtoList(ids, dataHolder);
        attributeWithValueDtoDataCollector.collectDataBySearchAttributeWithValueQto(
                dataHolder, qto);
        attributeWithValueDtoDataAssembler.assembleData(result, dataHolder);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
