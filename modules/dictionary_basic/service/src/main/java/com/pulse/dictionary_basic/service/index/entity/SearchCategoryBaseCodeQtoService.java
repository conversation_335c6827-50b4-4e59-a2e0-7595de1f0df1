package com.pulse.dictionary_basic.service.index.entity;

import com.pulse.dictionary_basic.persist.mapper.SearchCategoryBaseCodeQtoDao;
import com.pulse.dictionary_basic.persist.qto.SearchCategoryBaseCodeQto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = true, uuid = "fd3054aa-b58c-4c03-86e5-0b8c1d59a297|QTO|SERVICE")
public class SearchCategoryBaseCodeQtoService {
    @AutoGenerated(locked = true)
    @Resource
    private SearchCategoryBaseCodeQtoDao searchCategoryBaseCodeMapper;

    /** 计算总数量 */
    @AutoGenerated(locked = true)
    public Integer count(SearchCategoryBaseCodeQto qto) {
        return searchCategoryBaseCodeMapper.count(qto);
    }

    /** 不分页查询入口 */
    @AutoGenerated(locked = true, uuid = "fd3054aa-b58c-4c03-86e5-0b8c1d59a297-query")
    public List<String> query(SearchCategoryBaseCodeQto qto) {
        return searchCategoryBaseCodeMapper.query(qto);
    }
}
