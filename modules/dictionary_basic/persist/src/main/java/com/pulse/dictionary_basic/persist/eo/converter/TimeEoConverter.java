package com.pulse.dictionary_basic.persist.eo.converter;

import cn.hutool.core.util.StrUtil;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.dictionary_basic.persist.eo.TimeEo;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.utils.JsonUtils;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/** converter for TimeEo */
@Converter
@AutoGenerated(locked = true, uuid = "d9cc6087-7c9a-348b-9a0b-debffe378940")
public class TimeEoConverter implements AttributeConverter<TimeEo, String> {

    /** convert DB column to TimeEo */
    @AutoGenerated(locked = true)
    public String convertToDatabaseColumn(TimeEo timeEo) {
        if (timeEo == null) {
            return new String();
        } else {
            return JsonUtils.toJson(timeEo);
        }
    }

    /** convert DB column to TimeEo */
    @AutoGenerated(locked = true)
    public TimeEo convertToEntityAttribute(String timeEoJson) {
        if (StrUtil.isEmpty(timeEoJson)) {
            return null;
        } else {
            return JsonUtils.readObject(timeEoJson, new TypeReference<TimeEo>() {});
        }
    }
}
