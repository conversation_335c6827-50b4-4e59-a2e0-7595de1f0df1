package com.pulse.orders.manager;

import com.pulse.orders.manager.dto.OrderBaseDto;
import com.vs.code.AutoGenerated;

import java.util.List;

@AutoGenerated(locked = false, uuid = "e67040b3-175b-4247-af9b-eda13cd78145|DTO|MANAGER")
public interface OrderBaseDtoManager {

    @AutoGenerated(locked = true, uuid = "0290094b-f5ca-3085-acbf-b49f636653b6")
    List<OrderBaseDto> getByParentOrderIds(List<String> parentOrderId);

    @AutoGenerated(locked = true, uuid = "0937f2db-d3a5-38e5-9c87-424796b9ca54")
    List<OrderBaseDto> getByPrescriptionId(String prescriptionId);

    @AutoGenerated(locked = true, uuid = "1c7a057a-960a-30d3-b614-b676ac47438c")
    List<OrderBaseDto> getByOutpVisitIds(List<String> outpVisitId);

    @AutoGenerated(locked = true, uuid = "88d168a4-695a-3abd-8101-07023901d993")
    List<OrderBaseDto> getByPatientId(String patientId);

    @AutoGenerated(locked = true, uuid = "a8efaf15-3ec1-3923-b83a-35038ad296e8")
    List<OrderBaseDto> getByIds(List<String> id);

    @AutoGenerated(locked = true, uuid = "acf15db4-c2d8-3e9c-90a9-4b26df7851f3")
    List<OrderBaseDto> getByPatientIds(List<String> patientId);

    @AutoGenerated(locked = true, uuid = "b091f124-a93b-36d9-839e-baed707c9ac5")
    List<OrderBaseDto> getByParentOrderId(String parentOrderId);

    @AutoGenerated(locked = true, uuid = "b1839ff2-1a63-329c-928f-22592bd72241")
    List<OrderBaseDto> getByPrescriptionIds(List<String> prescriptionId);

    @AutoGenerated(locked = true, uuid = "d6270a55-4a19-3989-bfec-9e90d531a4bd")
    OrderBaseDto getById(String id);

    @AutoGenerated(locked = true, uuid = "fec1c883-b5da-3b8c-a270-d183d9d13587")
    List<OrderBaseDto> getByOutpVisitId(String outpVisitId);
}
