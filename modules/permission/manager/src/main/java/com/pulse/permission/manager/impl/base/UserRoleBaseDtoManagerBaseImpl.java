package com.pulse.permission.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.permission.manager.UserRoleBaseDtoManager;
import com.pulse.permission.manager.converter.UserRoleBaseDtoConverter;
import com.pulse.permission.manager.dto.UserRoleBaseDto;
import com.pulse.permission.persist.dos.UserRole;
import com.pulse.permission.persist.mapper.UserRoleDao;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "29a75c39-7143-4a26-81f6-68298d05a61b|DTO|BASE_MANAGER_IMPL")
public abstract class UserRoleBaseDtoManagerBaseImpl implements UserRoleBaseDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private UserRoleBaseDtoConverter userRoleBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private UserRoleDao userRoleDao;

    @AutoGenerated(locked = true, uuid = "0e88adc2-6aa2-3aa0-b463-bcf5188f6a64")
    @Override
    public List<UserRoleBaseDto> getByEnableFlag(Boolean enableFlag) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<UserRoleBaseDto> userRoleBaseDtoList = getByEnableFlags(Arrays.asList(enableFlag));
        return userRoleBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "0ea4c06c-57c9-30af-b7c3-fc47871f8ea5")
    @Override
    public UserRoleBaseDto getByUserIdAndRoleId(UserRole.RoleIdAndUserId var) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<UserRoleBaseDto> ret = getByUserIdsAndRoleIds(Arrays.asList(var));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        UserRoleBaseDto userRoleBaseDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return userRoleBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "3e28cf4f-72ef-39dc-b5c3-9dc22a5c4283")
    @Override
    public List<UserRoleBaseDto> getByUserIdsAndRoleIds(List<UserRole.RoleIdAndUserId> var) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(var)) {
            return Collections.emptyList();
        }

        List<UserRole> userRoleList = userRoleDao.getByUserIdsAndRoleIds(var);
        if (CollectionUtil.isEmpty(userRoleList)) {
            return Collections.emptyList();
        }

        return doConvertFromUserRoleToUserRoleBaseDto(userRoleList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "79670692-63c0-3c30-b2f2-947dd168d315")
    @Override
    public List<UserRoleBaseDto> getByUserIds(List<String> userId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(userId)) {
            return Collections.emptyList();
        }

        List<UserRole> userRoleList = userRoleDao.getByUserIds(userId);
        if (CollectionUtil.isEmpty(userRoleList)) {
            return Collections.emptyList();
        }

        return doConvertFromUserRoleToUserRoleBaseDto(userRoleList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "79bb391f-5c8e-3746-bfe1-bef97b0dba91")
    @Override
    public List<UserRoleBaseDto> getByRoleIds(List<String> roleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(roleId)) {
            return Collections.emptyList();
        }

        List<UserRole> userRoleList = userRoleDao.getByRoleIds(roleId);
        if (CollectionUtil.isEmpty(userRoleList)) {
            return Collections.emptyList();
        }

        return doConvertFromUserRoleToUserRoleBaseDto(userRoleList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "98ef6c9b-6c3a-37b1-9011-a982ddbfabbe")
    @Override
    public List<UserRoleBaseDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<UserRole> userRoleList = userRoleDao.getByIds(id);
        if (CollectionUtil.isEmpty(userRoleList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, UserRole> userRoleMap =
                userRoleList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        userRoleList =
                id.stream()
                        .map(i -> userRoleMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromUserRoleToUserRoleBaseDto(userRoleList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "9a113729-adc7-39a6-951d-c329a8e98d6a")
    @Override
    public List<UserRoleBaseDto> getByUserId(String userId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<UserRoleBaseDto> userRoleBaseDtoList = getByUserIds(Arrays.asList(userId));
        return userRoleBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "bb6ad989-b8b6-3eb9-bcdf-25b598a51942")
    @Override
    public UserRoleBaseDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<UserRoleBaseDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        UserRoleBaseDto userRoleBaseDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return userRoleBaseDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "c8c409ec-81dc-3dd7-9916-6c3b1dcbf5a4")
    public List<UserRoleBaseDto> doConvertFromUserRoleToUserRoleBaseDto(
            List<UserRole> userRoleList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(userRoleList)) {
            return Collections.emptyList();
        }

        Map<String, UserRoleBaseDto> dtoMap =
                userRoleBaseDtoConverter.convertFromUserRoleToUserRoleBaseDto(userRoleList).stream()
                        .collect(
                                Collectors.toMap(
                                        UserRoleBaseDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<UserRoleBaseDto> userRoleBaseDtoList = new ArrayList<>();
        for (UserRole i : userRoleList) {
            UserRoleBaseDto userRoleBaseDto = dtoMap.get(i.getId());
            if (userRoleBaseDto == null) {
                continue;
            }

            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            userRoleBaseDtoList.add(userRoleBaseDto);
        }
        return userRoleBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "ceac1f2e-6a71-3c8f-bad7-309414c45e08")
    @Override
    public List<UserRoleBaseDto> getByEnableFlags(List<Boolean> enableFlag) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(enableFlag)) {
            return Collections.emptyList();
        }

        List<UserRole> userRoleList = userRoleDao.getByEnableFlags(enableFlag);
        if (CollectionUtil.isEmpty(userRoleList)) {
            return Collections.emptyList();
        }

        return doConvertFromUserRoleToUserRoleBaseDto(userRoleList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "d4bede8f-6c5f-324b-801c-928bc7773d38")
    @Override
    public List<UserRoleBaseDto> getByRoleId(String roleId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<UserRoleBaseDto> userRoleBaseDtoList = getByRoleIds(Arrays.asList(roleId));
        return userRoleBaseDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
